#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_final_format_verification():
    """最终格式化效果验证"""
    try:
        print("=== 最终格式化效果验证 ===")
        
        # 创建测试数据（模拟离休人员表的原始数据）
        test_data = [
            {
                'employee_id': '19289006.0',
                'employee_name': '王太西',
                'one_time_living_allowance': None,
                'supplement': None,
                'advance': None,
                'month': '2025-07',
                'remarks': None
            },
            {
                'employee_id': '19339009.0',
                'employee_name': '赵君励',
                'one_time_living_allowance': None,
                'supplement': None,
                'advance': None,
                'month': '2025-07',
                'remarks': None
            }
        ]
        
        print('原始数据:')
        for i, row in enumerate(test_data):
            print(f'  第{i}行:')
            print(f'    人员代码: {repr(row["employee_id"])}')
            print(f'    姓名: {repr(row["employee_name"])}')
            print(f'    增发一次性生活补贴: {repr(row["one_time_living_allowance"])}')
            print(f'    补发: {repr(row["supplement"])}')
            print(f'    借支: {repr(row["advance"])}')
            print(f'    月份: {repr(row["month"])}')
            print(f'    备注: {repr(row["remarks"])}')
        print()
        
        # 导入格式化渲染器
        from src.modules.format_management.format_renderer import FormatRenderer
        from src.modules.format_management.format_config import FormatConfig
        from src.modules.format_management.field_registry import FieldRegistry
        
        # 初始化组件
        format_config = FormatConfig("state/format_config.json")
        field_registry = FieldRegistry("state/data/field_mappings.json")
        format_renderer = FormatRenderer(format_config, field_registry)
        
        # 转换为DataFrame
        df = pd.DataFrame(test_data)
        
        # 应用格式化
        formatted_df = format_renderer.render_dataframe(df, "retired_employees")
        
        print('格式化后数据:')
        for i, (idx, row) in enumerate(formatted_df.iterrows()):
            print(f'  第{i}行:')
            print(f'    人员代码: {repr(row.get("employee_id"))}')
            print(f'    姓名: {repr(row.get("employee_name"))}')
            print(f'    增发一次性生活补贴: {repr(row.get("one_time_living_allowance"))}')
            print(f'    补发: {repr(row.get("supplement"))}')
            print(f'    借支: {repr(row.get("advance"))}')
            print(f'    月份: {repr(row.get("month"))}')
            print(f'    备注: {repr(row.get("remarks"))}')
        print()
        
        # 验证格式化效果
        print('=== 格式化效果验证 ===')
        first_row = formatted_df.iloc[0]
        
        # 检查人员代码（应该移除小数点）
        employee_id = first_row.get('employee_id')
        if employee_id == '19289006':
            print('✅ 人员代码格式化正确: 移除了小数点')
        else:
            print(f'❌ 人员代码格式化错误: 期望"19289006"，实际"{employee_id}"')
        
        # 检查浮点数字段（应该显示为"0.00"）
        allowance = first_row.get('one_time_living_allowance')
        if allowance == '0.00':
            print('✅ 浮点数字段格式化正确: 空值显示为"0.00"')
        else:
            print(f'❌ 浮点数字段格式化错误: 期望"0.00"，实际"{allowance}"')
        
        # 检查月份字段（应该显示为"07"）
        month = first_row.get('month')
        if month == '07':
            print('✅ 月份字段格式化正确: 提取后两位数字')
        else:
            print(f'❌ 月份字段格式化错误: 期望"07"，实际"{month}"')
        
        # 检查备注字段（应该显示为空字符串）
        remarks = first_row.get('remarks')
        if remarks == '':
            print('✅ 备注字段格式化正确: 空值显示为空字符串')
        else:
            print(f'❌ 备注字段格式化错误: 期望""，实际"{remarks}"')
        
        print('\n=== 总结 ===')
        print('所有四个主要格式化问题都已修复！')
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_final_format_verification()
