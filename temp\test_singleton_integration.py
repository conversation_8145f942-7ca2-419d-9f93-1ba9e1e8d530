#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试单例格式管理器集成到系统
验证性能改进效果
"""

import sys
import os
import time

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

def test_singleton_format_manager_integration():
    """测试单例格式管理器集成效果"""
    print("[测试] 开始测试单例格式管理器集成...")
    
    try:
        # 导入单例格式管理器
        from temp.format_manager_singleton_fix import get_singleton_master_format_manager
        
        # 测试多次获取实例的性能
        print("[性能测试] 测试格式管理器获取性能...")
        
        # 第一次获取（初始化）
        start_time = time.time()
        manager1 = get_singleton_master_format_manager()
        first_time = (time.time() - start_time) * 1000
        print(f"首次获取时间: {first_time:.2f}ms")
        
        # 后续获取（单例复用）
        times = []
        for i in range(10):
            start_time = time.time()
            manager = get_singleton_master_format_manager()
            elapsed = (time.time() - start_time) * 1000
            times.append(elapsed)
            
            # 验证是同一个实例
            assert manager is manager1, f"实例不一致，第{i+1}次获取失败"
        
        avg_time = sum(times) / len(times)
        print(f"后续获取平均时间: {avg_time:.4f}ms")
        if first_time > 0:
            performance_improvement = ((first_time - avg_time) / first_time * 100)
            print(f"性能提升: {performance_improvement:.1f}%")
        else:
            print("性能提升: 单例访问极快，无法计算提升比例")
        
        # 测试格式化功能
        print("[功能测试] 测试格式化功能...")
        
        try:
            # 创建测试数据
            import pandas as pd
            test_data = pd.DataFrame({
                'employee_id': ['E001', 'E002'],
                'employee_name': ['张三', '李四'],
                'salary': [5000, 6000]
            })
            
            # 测试格式化
            formatted_data = manager1.format_table_data(test_data, 'active_employees')
            print(f"格式化测试完成，原始行数: {len(test_data)}, 格式化后行数: {len(formatted_data)}")
        except ImportError:
            print("pandas不可用，跳过数据格式化测试")
            # 使用简单数据测试
            test_data = [{'id': 1, 'name': 'test'}]
            formatted_data = manager1.format_table_data(test_data, 'active_employees')
            print("格式化测试完成（使用列表数据）")
        
        print("[测试] 单例格式管理器集成测试成功！")
        return True
        
    except Exception as e:
        print(f"[错误] 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_singleton_format_manager_integration()
    print(f"\n测试结果: {'成功' if success else '失败'}")