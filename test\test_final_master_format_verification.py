#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_final_master_format_verification():
    """最终验证主格式化管理器修复效果"""
    try:
        print("=== 最终验证主格式化管理器修复效果 ===")
        
        # 创建完整的测试数据（模拟实际离休人员表数据）
        test_data = pd.DataFrame([
            {
                'sequence_number': 1,
                'employee_id': '19289006.0',
                'employee_name': '王太西',
                'department': '离退休人员管理处',
                'basic_retirement_salary': 3500.50,
                'balance_allowance': 200.00,
                'living_allowance': 150.75,
                'housing_allowance': 800.00,
                'property_allowance': 100.00,
                'retirement_allowance': 300.25,
                'nursing_fee': 0.0,
                'one_time_living_allowance': None,  # 问题字段1：应显示为"0.00"
                'supplement': None,                 # 问题字段2：应显示为"0.00"
                'total': 5051.50,
                'advance': None,                    # 问题字段3：应显示为"0.00"
                'remarks': None,                    # 问题字段4：应显示为空字符串
                'month': '2025-07',                 # 问题字段5：应显示为"07"
                'year': '2025',                     # 问题字段6：应显示为"2025"
            },
            {
                'sequence_number': 2,
                'employee_id': '19339009.0',
                'employee_name': '赵君励',
                'department': '离退休人员管理处',
                'basic_retirement_salary': 3200.00,
                'balance_allowance': 180.50,
                'living_allowance': 140.00,
                'housing_allowance': 750.00,
                'property_allowance': 90.00,
                'retirement_allowance': 280.00,
                'nursing_fee': 50.00,
                'one_time_living_allowance': None,
                'supplement': None,
                'total': 4690.50,
                'advance': None,
                'remarks': None,
                'month': '2025-07',
                'year': '2025',
            }
        ])
        
        print('原始数据样例:')
        print(f'  人员代码: {repr(test_data.iloc[0]["employee_id"])}')
        print(f'  增发一次性生活补贴: {repr(test_data.iloc[0]["one_time_living_allowance"])}')
        print(f'  补发: {repr(test_data.iloc[0]["supplement"])}')
        print(f'  借支: {repr(test_data.iloc[0]["advance"])}')
        print(f'  备注: {repr(test_data.iloc[0]["remarks"])}')
        print(f'  月份: {repr(test_data.iloc[0]["month"])}')
        print(f'  年份: {repr(test_data.iloc[0]["year"])}')
        print()
        
        # 导入主格式化管理器
        from src.modules.format_management.master_format_manager import get_master_format_manager
        
        # 获取主格式化管理器
        master_formatter = get_master_format_manager()
        
        print('✅ 主格式化管理器获取成功')
        
        # 执行表格数据格式化
        formatted_df = master_formatter.format_table_data(test_data, "retired_employees")
        
        print('格式化后数据样例:')
        print(f'  人员代码: {repr(formatted_df.iloc[0]["employee_id"])}')
        print(f'  增发一次性生活补贴: {repr(formatted_df.iloc[0]["one_time_living_allowance"])}')
        print(f'  补发: {repr(formatted_df.iloc[0]["supplement"])}')
        print(f'  借支: {repr(formatted_df.iloc[0]["advance"])}')
        print(f'  备注: {repr(formatted_df.iloc[0]["remarks"])}')
        print(f'  月份: {repr(formatted_df.iloc[0]["month"])}')
        print(f'  年份: {repr(formatted_df.iloc[0]["year"])}')
        print()
        
        # 验证所有格式化问题
        print('=== 格式化问题验证 ===')
        
        success_count = 0
        total_checks = 6
        
        # 检查1：人员代码（移除小数点）
        employee_id_result = formatted_df.iloc[0]["employee_id"]
        if employee_id_result == '19289006':
            print('✅ 问题1已解决: 人员代码字段正确移除小数点')
            success_count += 1
        else:
            print(f'❌ 问题1未解决: 人员代码期望"19289006"，实际"{employee_id_result}"')
        
        # 检查2：增发一次性生活补贴（空值显示为"0.00"）
        allowance_result = formatted_df.iloc[0]["one_time_living_allowance"]
        if allowance_result == '0.00':
            print('✅ 问题2已解决: 增发一次性生活补贴空值正确显示为"0.00"')
            success_count += 1
        else:
            print(f'❌ 问题2未解决: 增发一次性生活补贴期望"0.00"，实际"{allowance_result}"')
        
        # 检查3：补发（空值显示为"0.00"）
        supplement_result = formatted_df.iloc[0]["supplement"]
        if supplement_result == '0.00':
            print('✅ 问题3已解决: 补发字段空值正确显示为"0.00"')
            success_count += 1
        else:
            print(f'❌ 问题3未解决: 补发字段期望"0.00"，实际"{supplement_result}"')
        
        # 检查4：借支（空值显示为"0.00"）
        advance_result = formatted_df.iloc[0]["advance"]
        if advance_result == '0.00':
            print('✅ 问题4已解决: 借支字段空值正确显示为"0.00"')
            success_count += 1
        else:
            print(f'❌ 问题4未解决: 借支字段期望"0.00"，实际"{advance_result}"')
        
        # 检查5：月份（提取后两位）
        month_result = formatted_df.iloc[0]["month"]
        if month_result == '07':
            print('✅ 问题5已解决: 月份字段正确提取后两位显示为"07"')
            success_count += 1
        else:
            print(f'❌ 问题5未解决: 月份字段期望"07"，实际"{month_result}"')
        
        # 检查6：备注（空值显示为空字符串）
        remarks_result = formatted_df.iloc[0]["remarks"]
        if remarks_result == '':
            print('✅ 问题6已解决: 备注字段空值正确显示为空字符串')
            success_count += 1
        else:
            print(f'❌ 问题6未解决: 备注字段期望""，实际"{remarks_result}"')
        
        print()
        print('=== 最终验证结果 ===')
        print(f'成功解决: {success_count}/{total_checks} 个问题')
        
        if success_count == total_checks:
            print('🎉 所有格式化问题已完全解决！')
            print('✅ 主格式化管理器修复成功，可以在实际应用中正常工作')
        else:
            print(f'⚠️  还有 {total_checks - success_count} 个问题需要进一步修复')
        
        print('\n=== 测试完成 ===')
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_final_master_format_verification()
