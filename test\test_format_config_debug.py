#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_format_config_debug():
    """调试格式配置问题"""
    try:
        print("=== 调试格式配置问题 ===")
        
        # 导入格式配置
        from src.modules.format_management.format_config import FormatConfig
        
        # 初始化格式配置
        format_config = FormatConfig("state/format_config.json")
        
        print('✅ 格式配置初始化成功')
        
        # 测试获取字符串格式规则
        print('\n=== 测试字符串格式规则 ===')
        
        # 1. 测试默认字符串格式
        default_string_rules = format_config.get_format_rules('string')
        print(f'默认字符串格式规则: {default_string_rules}')
        
        # 2. 测试离休人员表的字符串格式
        retired_string_rules = format_config.get_format_rules('string', 'retired_employees')
        print(f'离休人员表字符串格式规则: {retired_string_rules}')
        
        # 3. 检查empty_display配置
        if default_string_rules:
            empty_display = default_string_rules.get('empty_display', 'NOT_FOUND')
            print(f'默认字符串empty_display: {repr(empty_display)}')
        
        if retired_string_rules:
            empty_display = retired_string_rules.get('empty_display', 'NOT_FOUND')
            print(f'离休人员表字符串empty_display: {repr(empty_display)}')
        
        # 4. 测试其他格式类型
        print('\n=== 测试其他格式类型 ===')
        
        float_rules = format_config.get_format_rules('float')
        print(f'浮点数格式规则: {float_rules}')
        
        month_string_rules = format_config.get_format_rules('month_string')
        print(f'月份字符串格式规则: {month_string_rules}')
        
        # 5. 检查配置文件内容
        print('\n=== 检查配置文件内容 ===')
        
        # 获取所有默认格式
        default_formats = format_config.get_config('default_formats')
        print(f'所有默认格式: {default_formats}')
        
        # 检查是否有表特定配置
        retired_config = format_config.get_config('retired_employees_format_config')
        print(f'离休人员表特定配置: {retired_config}')
        
        # 6. 测试FormatRenderer
        print('\n=== 测试FormatRenderer ===')
        
        from src.modules.format_management.format_renderer import FormatRenderer
        from src.modules.format_management.field_registry import FieldRegistry
        
        field_registry = FieldRegistry("state/data/field_mappings.json")
        format_renderer = FormatRenderer(format_config, field_registry)
        
        # 测试字符串值渲染
        test_value = None
        rendered_value = format_renderer.render_value(test_value, 'string', 'remarks')
        print(f'备注字段渲染结果: None → "{rendered_value}"')
        
        # 测试浮点数值渲染
        test_float_value = None
        rendered_float_value = format_renderer.render_value(test_float_value, 'float', 'one_time_living_allowance')
        print(f'增发一次性生活补贴渲染结果: None → "{rendered_float_value}"')
        
        print('\n=== 调试完成 ===')
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_format_config_debug()
