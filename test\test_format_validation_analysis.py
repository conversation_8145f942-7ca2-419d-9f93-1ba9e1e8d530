#!/usr/bin/env python3
"""
分析格式化验证逻辑与实际格式化结果的差异
"""

import sys
import os
import pandas as pd
import numpy as np
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_format_validation_mismatch():
    """测试格式化验证逻辑与实际格式化结果的差异"""
    print("🔧 [分析] 开始分析格式化验证逻辑与实际格式化结果的差异...")
    
    try:
        # 1. 创建测试数据
        test_data = pd.DataFrame({
            '人员代码': [19990089.0, 19990090.0, None],  # 浮点数形式的人员代码
            '补发': [None, 0.0, 100.5],  # 浮点数字段，包含空值
            '借支': [np.nan, 0, 200.75],  # 浮点数字段，包含NaN
            '月份': [202501, 202502, None],  # 月份字段
            '备注': [None, '', '测试备注']  # 字符串字段
        })
        
        print("✅ [分析] 测试数据创建成功")
        print("原始数据:")
        print(test_data)
        print()
        
        # 2. 使用UnifiedFormatManager进行格式化
        from src.modules.format_management.unified_format_manager import UnifiedFormatManager
        format_manager = UnifiedFormatManager()
        
        formatted_data = format_manager.format_data(test_data, "retired_employees", data_source="test")
        
        print("格式化后数据:")
        print(formatted_data)
        print()
        
        # 3. 使用MasterFormatManager的验证逻辑进行验证
        from src.modules.format_management.master_format_manager import get_master_format_manager
        master_formatter = get_master_format_manager()
        
        # 验证格式化完整性
        is_complete = master_formatter._verify_formatting_completeness(formatted_data, "retired_employees")
        print(f"格式化完整性验证结果: {is_complete}")
        print()
        
        # 4. 逐字段分析验证结果
        print("🔍 [详细分析] 逐字段验证分析:")
        
        if len(formatted_data) > 0:
            first_row = formatted_data.iloc[0]
            
            # 分析人员代码字段
            if '人员代码' in formatted_data.columns:
                value = first_row['人员代码']
                is_valid = master_formatter._is_employee_code_properly_formatted(value)
                print(f"  人员代码: '{value}' (类型: {type(value).__name__}) -> 验证结果: {is_valid}")
            
            # 分析补发字段
            if '补发' in formatted_data.columns:
                value = first_row['补发']
                is_valid = master_formatter._is_float_properly_formatted(value)
                print(f"  补发: '{value}' (类型: {type(value).__name__}) -> 验证结果: {is_valid}")
            
            # 分析借支字段
            if '借支' in formatted_data.columns:
                value = first_row['借支']
                is_valid = master_formatter._is_float_properly_formatted(value)
                print(f"  借支: '{value}' (类型: {type(value).__name__}) -> 验证结果: {is_valid}")
            
            # 分析月份字段
            if '月份' in formatted_data.columns:
                value = first_row['月份']
                is_valid = master_formatter._is_month_properly_formatted(value)
                print(f"  月份: '{value}' (类型: {type(value).__name__}) -> 验证结果: {is_valid}")
        
        print()
        
        # 5. 分析配置文件
        print("🔍 [配置分析] 当前格式化配置:")
        format_config = format_manager.format_config.get_format_config()
        print(f"  浮点数配置: {format_config.get('float_format', {})}")
        print(f"  字符串配置: {format_config.get('string_format', {})}")
        
        return True
        
    except Exception as e:
        print(f"❌ [分析] 分析过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_individual_validation_methods():
    """测试各个验证方法的行为"""
    print("\n🔧 [测试] 测试各个验证方法的行为...")
    
    try:
        from src.modules.format_management.master_format_manager import get_master_format_manager
        master_formatter = get_master_format_manager()
        
        # 测试浮点数验证
        print("浮点数验证测试:")
        float_test_cases = [
            None, '', '0.00', '100.50', '0', '100', '100.5', 'abc', '19990089.0'
        ]
        
        for case in float_test_cases:
            result = master_formatter._is_float_properly_formatted(case)
            print(f"  '{case}' -> {result}")
        
        print("\n人员代码验证测试:")
        employee_code_test_cases = [
            None, '', '19990089', '19990089.0', 19990089, 19990089.0, 'abc123'
        ]
        
        for case in employee_code_test_cases:
            result = master_formatter._is_employee_code_properly_formatted(case)
            print(f"  '{case}' (类型: {type(case).__name__}) -> {result}")
        
        print("\n月份验证测试:")
        month_test_cases = [
            None, '', '01', '12', '1', '123', 1, 12, 202501
        ]
        
        for case in month_test_cases:
            result = master_formatter._is_month_properly_formatted(case)
            print(f"  '{case}' (类型: {type(case).__name__}) -> {result}")
        
        return True
        
    except Exception as e:
        print(f"❌ [测试] 验证方法测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 [分析] 开始格式化验证逻辑分析")
    print("=" * 60)
    
    # 测试1: 格式化验证差异分析
    test1_result = test_format_validation_mismatch()
    
    # 测试2: 各个验证方法行为测试
    test2_result = test_individual_validation_methods()
    
    print("\n" + "=" * 60)
    print("📊 [分析结果]")
    print(f"  格式化验证差异分析: {'✅ 完成' if test1_result else '❌ 失败'}")
    print(f"  验证方法行为测试: {'✅ 完成' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 [分析] 分析完成！请查看上述结果找出验证逻辑问题")
        sys.exit(0)
    else:
        print("\n💥 [分析] 分析过程中出现问题")
        sys.exit(1)
