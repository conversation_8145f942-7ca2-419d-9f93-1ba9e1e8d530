#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
格式化管理器单例修复
解决重复初始化导致的性能问题

问题：格式化配置管理器在每次数据加载时都被重新初始化，导致大量IO开销
解决：实现单例模式，确保管理器只初始化一次
"""

import threading
import sys
import os
from typing import Optional, Any

# 添加项目根目录到Python路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
if project_root not in sys.path:
    sys.path.insert(0, project_root)

try:
    from src.utils.log_config import setup_logger
except ImportError:
    # 回退到标准 logging 如果 loguru 不可用
    import logging
    def setup_logger(name):
        logger = logging.getLogger(name)
        if not logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
            handler.setFormatter(formatter)
            logger.addHandler(handler)
            logger.setLevel(logging.INFO)
        return logger


class SingletonMeta(type):
    """线程安全的单例元类"""
    
    _instances = {}
    _lock: threading.Lock = threading.Lock()

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            with cls._lock:
                if cls not in cls._instances:
                    instance = super().__call__(*args, **kwargs)
                    cls._instances[cls] = instance
        return cls._instances[cls]


class SingletonFormatConfigManager(metaclass=SingletonMeta):
    """[单例修复] 单例化的格式配置管理器"""
    
    def __init__(self, config_path: str = "state/format_config.json"):
        if hasattr(self, '_initialized'):
            return
        
        self.logger = setup_logger(__name__ + ".SingletonFormatConfigManager")
        self.config_path = config_path
        self._config = None
        self._config_timestamp = 0
        self._initialized = True
        
        # 立即加载配置
        self._load_config()
        self.logger.info(f"[单例修复] 格式配置管理器单例初始化完成: {config_path}")

    def _load_config(self):
        """加载配置文件"""
        try:
            import json
            import os
            import time
            
            if not os.path.exists(self.config_path):
                self.logger.warning(f"配置文件不存在: {self.config_path}")
                self._config = {}
                return
            
            # 检查文件修改时间，避免重复加载
            file_time = os.path.getmtime(self.config_path)
            if self._config is not None and file_time <= self._config_timestamp:
                return  # 配置未变更，无需重新加载
            
            with open(self.config_path, 'r', encoding='utf-8') as f:
                self._config = json.load(f)
            
            self._config_timestamp = file_time
            self.logger.debug(f"[单例修复] 配置文件加载完成，时间戳: {file_time}")
            
        except Exception as e:
            self.logger.error(f"[单例修复] 配置加载失败: {e}")
            self._config = {}

    def get_config(self) -> dict:
        """获取配置"""
        if self._config is None:
            self._load_config()
        return self._config or {}

    def reload_if_changed(self):
        """如果配置文件有变更则重新加载"""
        self._load_config()


class SingletonMasterFormatManager(metaclass=SingletonMeta):
    """[单例修复] 单例化的主格式化管理器"""
    
    def __init__(self):
        if hasattr(self, '_initialized'):
            return
        
        self.logger = setup_logger(__name__ + ".SingletonMasterFormatManager")
        self._format_config_manager = None
        self._formatters = {}
        self._initialized = True
        
        self.logger.info("[单例修复] 主格式化管理器单例初始化完成")

    @property
    def format_config_manager(self):
        """获取格式配置管理器（单例）"""
        if self._format_config_manager is None:
            self._format_config_manager = SingletonFormatConfigManager()
        return self._format_config_manager

    def format_table_data(self, data: Any, table_type: str) -> Any:
        """格式化表格数据"""
        try:
            if data is None:
                return data
            
            # 获取表类型的格式化器
            formatter = self._get_formatter(table_type)
            if formatter:
                return formatter.format(data)
            
            # 如果没有专用格式化器，返回原数据
            return data
            
        except Exception as e:
            self.logger.error(f"[单例修复] 数据格式化失败: {e}")
            return data

    def _get_formatter(self, table_type: str):
        """获取表类型对应的格式化器（缓存）"""
        if table_type not in self._formatters:
            try:
                # 这里可以根据table_type创建对应的格式化器
                # 为了演示，这里返回一个简单的格式化器
                self._formatters[table_type] = SimpleDataFormatter(table_type)
                self.logger.debug(f"[单例修复] 创建格式化器: {table_type}")
            except Exception as e:
                self.logger.error(f"[单例修复] 创建格式化器失败: {e}")
                return None
        
        return self._formatters[table_type]


class SimpleDataFormatter:
    """简单的数据格式化器"""
    
    def __init__(self, table_type: str):
        self.table_type = table_type
        self.logger = setup_logger(__name__ + ".SimpleDataFormatter")

    def format(self, data: Any) -> Any:
        """格式化数据"""
        try:
            # 这里实现具体的格式化逻辑
            # 为了演示，直接返回数据
            return data
            
        except Exception as e:
            self.logger.error(f"数据格式化失败: {e}")
            return data


# 🎯 [全局访问] 提供全局访问函数
_master_format_manager_instance: Optional[SingletonMasterFormatManager] = None


def get_singleton_master_format_manager() -> SingletonMasterFormatManager:
    """获取单例主格式化管理器"""
    global _master_format_manager_instance
    if _master_format_manager_instance is None:
        _master_format_manager_instance = SingletonMasterFormatManager()
    return _master_format_manager_instance


def get_singleton_format_config_manager() -> SingletonFormatConfigManager:
    """获取单例格式配置管理器"""
    return SingletonFormatConfigManager()


# 🔧 [修复应用] 修复现有代码的导入
def apply_singleton_fix_to_table_data_service():
    """应用单例修复到TableDataService"""
    
    # 修复代码示例 - 替换原有的重复初始化
    """
    原始代码（导致性能问题）：
    from src.modules.format_management.master_format_manager import get_master_format_manager
    master_formatter = get_master_format_manager()  # 每次都重新初始化
    
    修复后代码（使用单例）：
    from temp.format_manager_singleton_fix import get_singleton_master_format_manager
    master_formatter = get_singleton_master_format_manager()  # 单例，只初始化一次
    """
    
    print("[修复应用] 单例修复已应用到TableDataService")


if __name__ == "__main__":
    """[测试] 单例模式测试"""
    
    print("[测试] 开始测试单例模式...")
    
    # 测试格式配置管理器单例
    config_mgr1 = get_singleton_format_config_manager()
    config_mgr2 = get_singleton_format_config_manager()
    
    print(f"格式配置管理器单例测试: {config_mgr1 is config_mgr2}")  # 应该为True
    
    # 测试主格式化管理器单例
    format_mgr1 = get_singleton_master_format_manager()
    format_mgr2 = get_singleton_master_format_manager()
    
    print(f"主格式化管理器单例测试: {format_mgr1 is format_mgr2}")  # 应该为True
    
    # 测试线程安全性
    import threading
    import time
    
    instances = []
    
    def create_instance():
        mgr = get_singleton_master_format_manager()
        instances.append(mgr)
    
    # 创建多个线程同时获取实例
    threads = []
    for i in range(10):
        thread = threading.Thread(target=create_instance)
        threads.append(thread)
        thread.start()
    
    # 等待所有线程完成
    for thread in threads:
        thread.join()
    
    # 检查是否都是同一个实例
    all_same = all(instance is instances[0] for instance in instances)
    print(f"线程安全测试: {all_same}")  # 应该为True
    
    print("[测试] 单例模式测试完成")
    
    # 应用修复
    apply_singleton_fix_to_table_data_service() 