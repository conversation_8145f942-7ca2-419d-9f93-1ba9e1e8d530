#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import pandas as pd
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_format_process():
    """测试格式化过程"""
    try:
        # 连接数据库
        conn = sqlite3.connect('data/db/salary_system.db')
        
        # 查询离休人员表的原始数据
        query = 'SELECT * FROM salary_data_2025_07_retired_employees LIMIT 2'
        df = pd.read_sql_query(query, conn)
        
        print('=== 原始数据 ===')
        print('数据形状:', df.shape)
        print('列名:', list(df.columns))
        print()
        
        # 转换为字典格式（模拟表格数据格式）
        data = df.to_dict('records')
        
        print('=== 原始数据（字典格式）===')
        for i, row in enumerate(data):
            print(f'第{i}行:')
            print(f'  employee_id: {repr(row.get("employee_id"))}')
            print(f'  employee_name: {repr(row.get("employee_name"))}')
            print(f'  one_time_living_allowance: {repr(row.get("one_time_living_allowance"))}')
            print(f'  supplement: {repr(row.get("supplement"))}')
            print(f'  advance: {repr(row.get("advance"))}')
            print(f'  month: {repr(row.get("month"))}')
            print(f'  remarks: {repr(row.get("remarks"))}')
            print()
        
        # 现在测试格式化管理器
        print('=== 测试格式化管理器 ===')
        
        # 导入格式化管理器
        from src.core.architecture_factory import ArchitectureFactory
        
        # 初始化架构工厂
        factory = ArchitectureFactory()
        factory.initialize_architecture()
        
        # 获取格式化管理器
        format_manager = factory.get_unified_format_manager()
        
        if format_manager:
            print('格式化管理器获取成功')
            
            # 尝试格式化数据
            table_type = "retired_employees"
            formatted_data, formatted_headers = format_manager.format_table_data(
                data, list(df.columns), table_type
            )
            
            print('=== 格式化后数据 ===')
            print('格式化数据长度:', len(formatted_data))
            print('格式化表头:', formatted_headers)
            print()
            
            for i, row in enumerate(formatted_data):
                print(f'第{i}行:')
                print(f'  人员代码: {repr(row.get("人员代码"))}')
                print(f'  姓名: {repr(row.get("姓名"))}')
                print(f'  增发一次性生活补贴: {repr(row.get("增发一次性生活补贴"))}')
                print(f'  补发: {repr(row.get("补发"))}')
                print(f'  借支: {repr(row.get("借支"))}')
                print(f'  月份: {repr(row.get("月份"))}')
                print(f'  备注: {repr(row.get("备注"))}')
                print()
                
            # 测试顺序检查逻辑
            print('=== 测试顺序检查逻辑 ===')
            if len(formatted_data) > 0 and len(data) > 0:
                original_first_id = data[0].get('工号') or data[0].get('employee_id') or str(data[0])[:20]
                formatted_first_id = formatted_data[0].get('工号') or formatted_data[0].get('employee_id') or formatted_data[0].get('人员代码') or str(formatted_data[0])[:20]
                
                print(f'原始第一行ID: {repr(original_first_id)}')
                print(f'格式化第一行ID: {repr(formatted_first_id)}')
                
                # 标准化ID值用于比较
                def normalize_id(id_value):
                    if id_value is None:
                        return None
                    id_str = str(id_value)
                    if '.' in id_str and id_str.replace('.', '').replace('-', '').isdigit():
                        try:
                            return str(int(float(id_str)))
                        except (ValueError, TypeError):
                            return id_str
                    return id_str
                
                normalized_original = normalize_id(original_first_id)
                normalized_formatted = normalize_id(formatted_first_id)
                
                print(f'标准化原始ID: {repr(normalized_original)}')
                print(f'标准化格式化ID: {repr(normalized_formatted)}')
                print(f'ID是否匹配: {normalized_original == normalized_formatted}')
        else:
            print('格式化管理器获取失败')
        
        conn.close()
        
    except Exception as e:
        print(f"错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_format_process()
