2025-07-31 18:22:18.346 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-07-31 18:22:18.346 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-07-31 18:22:18.346 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-07-31 18:22:18.346 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-07-31 18:22:18.346 | INFO     | src.utils.log_config:_log_initialization_info:214 | 日志文件路径: logs/salary_system.log
2025-07-31 18:22:18.346 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-07-31 18:22:20.885 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-07-31 18:22:20.885 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-07-31 18:22:20.885 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-07-31 18:22:20.885 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-07-31 18:22:20.885 | INFO     | __main__:setup_qt_exception_handling:218 | 🔧 [P0-修复] PyQt专用异常处理机制已启用
2025-07-31 18:22:20.901 | INFO     | __main__:setup_app_logging:349 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-07-31 18:22:20.901 | INFO     | __main__:main:413 | 初始化核心管理器...
2025-07-31 18:22:20.901 | INFO     | src.modules.system_config.config_manager:__init__:313 | 配置管理器初始化完成，配置文件: config.json
2025-07-31 18:22:20.901 | INFO     | src.modules.system_config.config_manager:load_config:340 | 正在加载配置文件: config.json
2025-07-31 18:22:20.901 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-07-31 18:22:20.901 | INFO     | src.modules.system_config.config_manager:load_config:352 | 配置文件加载成功
2025-07-31 18:22:20.901 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-07-31 18:22:20.948 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-07-31 18:22:20.948 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-07-31 18:22:20.948 | INFO     | src.modules.system_config.config_manager:__init__:313 | 配置管理器初始化完成，配置文件: config.json
2025-07-31 18:22:20.948 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:104 | 动态表管理器初始化完成
2025-07-31 18:22:20.948 | INFO     | __main__:main:418 | 核心管理器初始化完成。
2025-07-31 18:22:20.948 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-07-31 18:22:20.948 | INFO     | src.core.table_sort_state_manager:__init__:174 | 表级排序状态管理器初始化完成
2025-07-31 18:22:20.948 | INFO     | src.core.event_bus:__init__:227 | 事件总线初始化完成
2025-07-31 18:22:20.948 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-07-31 18:22:20.948 | INFO     | src.core.error_handler_manager:register_recovery_strategy:368 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-07-31 18:22:20.963 | INFO     | src.core.error_handler_manager:register_recovery_strategy:368 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-07-31 18:22:20.963 | INFO     | src.core.error_handler_manager:register_recovery_strategy:368 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-07-31 18:22:20.963 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:9574 | 🔧 [P2-3] 错误恢复策略注册完成
2025-07-31 18:22:20.963 | INFO     | src.core.event_bus:__init__:227 | 事件总线初始化完成
2025-07-31 18:22:20.963 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:9429 | 🔧 [P2-3] 错误处理机制设置完成
2025-07-31 18:22:20.963 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:9467 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-07-31 18:22:21.026 | INFO     | src.core.architecture_factory:__init__:64 | 架构重构工厂初始化完成
2025-07-31 18:22:21.026 | INFO     | src.core.architecture_factory:initialize_architecture:74 | 开始初始化架构重构系统...
2025-07-31 18:22:21.026 | INFO     | src.core.event_bus:__init__:227 | 事件总线初始化完成
2025-07-31 18:22:21.141 | WARNING  | src.modules.data_import.config_sync_manager:_do_config_initialization:139 | 创建了最小默认配置，可能需要重新导入数据以生成字段映射
2025-07-31 18:22:21.141 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-07-31 18:22:21.141 | INFO     | src.core.unified_state_manager:_load_state:465 | 状态文件不存在，使用默认状态
2025-07-31 18:22:21.141 | INFO     | src.core.unified_state_manager:__init__:177 | 统一状态管理器初始化完成
2025-07-31 18:22:21.141 | INFO     | src.core.unified_data_request_manager:__init__:190 | 统一数据请求管理器初始化完成
2025-07-31 18:22:21.141 | INFO     | src.core.unified_data_request_manager:__init__:190 | 统一数据请求管理器初始化完成
2025-07-31 18:22:21.141 | INFO     | src.core.unified_state_manager:_load_state:465 | 状态文件不存在，使用默认状态
2025-07-31 18:22:21.141 | INFO     | src.core.unified_state_manager:__init__:177 | 统一状态管理器初始化完成
2025-07-31 18:22:21.141 | INFO     | src.services.table_data_service:__init__:81 | 表格数据服务初始化完成
2025-07-31 18:22:21.141 | INFO     | src.core.architecture_factory:initialize_architecture:105 | 🎉 架构重构系统初始化成功！耗时: 114.7ms
2025-07-31 18:22:21.156 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-07-31 18:22:21.172 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-07-31 18:22:21.172 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-07-31 18:22:21.172 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-07-31 18:22:21.172 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-07-31 18:22:21.172 | INFO     | src.gui.prototype.prototype_main_window:__init__:3147 | 🚀 性能管理器已集成
2025-07-31 18:22:21.172 | INFO     | src.gui.prototype.prototype_main_window:__init__:3149 | ✅ 新架构集成成功！
2025-07-31 18:22:21.172 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3261 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-07-31 18:22:21.188 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3227 | ✅ 新架构事件监听器设置完成
2025-07-31 18:22:21.188 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-07-31 18:22:21.188 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:146 | 响应式布局管理器初始化完成
2025-07-31 18:22:21.188 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-07-31 18:22:21.422 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2360 | 菜单栏创建完成
2025-07-31 18:22:21.422 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-07-31 18:22:21.422 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-07-31 18:22:21.438 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-07-31 18:22:21.438 | INFO     | src.gui.prototype.prototype_main_window:__init__:2336 | 菜单栏管理器初始化完成
2025-07-31 18:22:21.438 | INFO     | src.gui.table_header_manager:__init__:104 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-07-31 18:22:21.438 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:4552 | 管理器设置完成，包含增强版表头管理器
2025-07-31 18:22:21.438 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:4557 | 🔧 开始应用窗口级Material Design样式...
2025-07-31 18:22:21.438 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-07-31 18:22:21.438 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-07-31 18:22:21.438 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:4564 | ✅ 窗口级样式应用成功
2025-07-31 18:22:21.438 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:4605 | ✅ 响应式样式监听设置完成
2025-07-31 18:22:21.438 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-07-31 18:22:21.453 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-07-31 18:22:21.469 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1236 | 开始从元数据动态加载工资数据...
2025-07-31 18:22:21.469 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1243 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-07-31 18:22:21.483 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:751 | 导航面板已重构：移除功能性导航，专注数据导航
2025-07-31 18:22:21.487 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:786 | 恢复导航状态: 0个展开项
2025-07-31 18:22:21.488 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表', '工资表 > 2025年']
2025-07-31 18:22:21.489 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:552 | 增强导航面板初始化完成
2025-07-31 18:22:21.749 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-07-31 18:22:21.750 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2046 | 🚨 [架构修复] 启用模型数据同步机制
2025-07-31 18:22:21.754 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1326 | 快捷键注册完成: 18/18 个
2025-07-31 18:22:21.754 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1714 | 拖拽排序管理器初始化完成
2025-07-31 18:22:21.755 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-07-31 18:22:21.756 | INFO     | src.core.unified_state_manager:_load_state:465 | 状态文件不存在，使用默认状态
2025-07-31 18:22:21.756 | INFO     | src.core.unified_state_manager:__init__:177 | 统一状态管理器初始化完成
2025-07-31 18:22:21.757 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2121 | 🧹 [代码清理] 格式管理器已迁移到MasterFormatManager
2025-07-31 18:22:21.769 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:337 | 🔧 [新架构] 成功加载 46 个字段映射
2025-07-31 18:22:21.769 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:99 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-07-31 18:22:21.771 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2163 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-07-31 18:22:21.773 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1510 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-07-31 18:22:21.773 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1511 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-07-31 18:22:21.774 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1512 | 🔧 [列宽保存修复] 配置文件存在: False
2025-07-31 18:22:21.775 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1513 | 🔧 [列宽保存修复] 父目录存在: True
2025-07-31 18:22:21.776 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1514 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-07-31 18:22:21.782 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2170 | 列宽管理器初始化完成
2025-07-31 18:22:21.782 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2201 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-07-31 18:22:21.785 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2184 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-07-31 18:22:21.786 | WARNING  | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1998 | 无法获取主窗口引用，使用备用方案显示空表格
2025-07-31 18:22:21.787 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4587 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-07-31 18:22:21.788 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2532 | 表格格式化完成: default_table, 类型: active_employees
2025-07-31 18:22:21.801 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-07-31 18:22:21.803 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:242 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-07-31 18:22:21.803 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2598 | [数据流追踪] 优化渲染完成: 0行 x 22列, 策略=empty_data, 耗时=0.0ms, 性能评级=excellent
2025-07-31 18:22:21.804 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-07-31 18:22:21.805 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 0 行, 22 列
2025-07-31 18:22:21.811 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2678 | 表格数据设置完成: 0 行, 耗时: 24.0ms
2025-07-31 18:22:21.811 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7115 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-31 18:22:21.813 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7128 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-31 18:22:21.813 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1618 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-07-31 18:22:21.814 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2020 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个
2025-07-31 18:22:21.836 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:343 | 分页组件Material Design样式应用成功
2025-07-31 18:22:21.892 | INFO     | src.gui.widgets.pagination_widget:__init__:174 | ✅ [防抖升级] 智能防抖系统已启用
2025-07-31 18:22:21.893 | INFO     | src.gui.widgets.pagination_widget:__init__:182 | 分页组件初始化完成
2025-07-31 18:22:21.956 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:523 | 控制面板按钮信号连接完成
2025-07-31 18:22:22.008 | INFO     | src.modules.system_config.config_manager:__init__:313 | 配置管理器初始化完成，配置文件: config.json
2025-07-31 18:22:22.013 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-07-31 18:22:22.013 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:4522 | 快捷键设置完成
2025-07-31 18:22:22.014 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:4511 | 主窗口UI设置完成。
2025-07-31 18:22:22.015 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:4708 | 🔧 [全局排序] 全局排序开关连接成功
2025-07-31 18:22:22.016 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:4740 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-07-31 18:22:22.016 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:4749 | ✅ 已连接分页组件事件到新架构
2025-07-31 18:22:22.018 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:4751 | 信号连接设置完成
2025-07-31 18:22:22.019 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:5679 | 已加载字段映射信息，共0个表的映射
2025-07-31 18:22:22.026 | WARNING  | src.gui.prototype.prototype_main_window:set_data:688 | 尝试设置空数据，将显示提示信息。
2025-07-31 18:22:22.027 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6895 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-07-31 18:22:22.028 | WARNING  | src.gui.prototype.prototype_main_window:set_data:672 | 🔧 [P0-2] 检测到递归设置数据，跳过以防止表头重影
2025-07-31 18:22:22.029 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6910 | 已显示标准空表格，表头数量: 22
2025-07-31 18:22:22.030 | INFO     | src.gui.widgets.pagination_widget:reset:595 | 分页状态已重置
2025-07-31 18:22:22.031 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6895 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-07-31 18:22:22.032 | WARNING  | src.gui.prototype.prototype_main_window:set_data:708 | 尝试设置空数据，将显示提示信息。
2025-07-31 18:22:22.032 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6895 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-07-31 18:22:22.033 | WARNING  | src.gui.prototype.prototype_main_window:set_data:672 | 🔧 [P0-2] 检测到递归设置数据，跳过以防止表头重影
2025-07-31 18:22:22.044 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6910 | 已显示标准空表格，表头数量: 22
2025-07-31 18:22:22.044 | INFO     | src.gui.widgets.pagination_widget:reset:595 | 分页状态已重置
2025-07-31 18:22:22.047 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6910 | 已显示标准空表格，表头数量: 22
2025-07-31 18:22:22.047 | INFO     | src.gui.prototype.prototype_main_window:__init__:3201 | 原型主窗口初始化完成
2025-07-31 18:22:22.229 | INFO     | __main__:main:440 | 应用程序启动成功
2025-07-31 18:22:22.235 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:7512 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-07-31 18:22:22.238 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:7526 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet', 'parent_opacity_effect']
2025-07-31 18:22:22.240 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:7941 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet', 'parent_opacity_effect']
2025-07-31 18:22:22.348 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:206 | 断点切换: sm (宽度: 1280px)
2025-07-31 18:22:22.348 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1838 | MainWorkspaceArea 响应式适配: sm
2025-07-31 18:22:22.367 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:1258 | 执行延迟的工资数据加载...
2025-07-31 18:22:22.368 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1539 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-07-31 18:22:22.373 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1547 | 找到工资表节点: 💰 工资表
2025-07-31 18:22:22.383 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-31 18:22:22.383 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:976 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-07-31 18:22:22.384 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:888 | 找到 3 个总表
2025-07-31 18:22:22.385 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1599 | 检测到数据库中没有工资数据表，直接使用兜底数据
2025-07-31 18:22:22.385 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1381 | 使用兜底数据加载导航
2025-07-31 18:22:22.386 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1197 | 执行延迟的自动选择最新数据...
2025-07-31 18:22:22.387 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1137 | 开始自动选择最新数据...
2025-07-31 18:22:22.387 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1036 | 开始获取最新工资数据路径...
2025-07-31 18:22:22.388 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-31 18:22:22.396 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1041 | 未找到任何工资数据表
2025-07-31 18:22:22.397 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1142 | 未找到最新工资数据路径，无法自动选择
2025-07-31 18:22:22.397 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1217 | 延迟自动选择最新数据失败，可能没有可用数据
2025-07-31 18:22:22.403 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:7512 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-07-31 18:22:22.439 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1597 | 🔧 [列宽保存修复] 准备保存列宽到文件: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-07-31 18:22:22.444 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1598 | 🔧 [列宽保存修复] 表名: default_table, 列数: 22
2025-07-31 18:22:22.447 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1603 | 🔧 [列宽保存修复] 列宽设置保存成功！文件大小: 904 字节
2025-07-31 18:22:22.450 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:7532 | 🔧 [P0-1] 智能显示亮度修复完成
2025-07-31 18:22:22.477 | WARNING  | src.gui.prototype.prototype_main_window:_check_method_call_protection:9513 | 🔧 [P1-2] 方法 _fix_display_brightness_after_data_refresh 每秒调用次数超限: 2
2025-07-31 18:22:22.478 | INFO     | src.gui.prototype.prototype_main_window:_apply_cooldown:9543 | 🔧 [P1-2] 对方法 _fix_display_brightness_after_data_refresh 应用 5.0秒 冷却期
2025-07-31 18:22:22.587 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1287 | 导航树刷新完成，重新执行自动选择...
2025-07-31 18:22:22.587 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1036 | 开始获取最新工资数据路径...
2025-07-31 18:22:22.587 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-31 18:22:22.587 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1041 | 未找到任何工资数据表
2025-07-31 18:22:22.587 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1292 | 未找到最新工资数据路径
2025-07-31 18:22:42.420 | INFO     | src.gui.style_manager:update_responsive_style:227 | 响应式样式更新: medium -> large
2025-07-31 18:22:42.421 | INFO     | src.gui.style_manager:update_responsive_style:238 | 响应式样式更新成功: large
2025-07-31 18:22:42.532 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:7512 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-07-31 18:22:42.650 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:7512 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-07-31 18:22:48.463 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:583 | 数据导入功能被触发，发出 import_requested 信号。
2025-07-31 18:22:48.463 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:4982 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 07月 > 全部在职人员。打开导入对话框。
2025-07-31 18:22:48.463 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-07-31 18:22:48.463 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:72 | 多Sheet导入器初始化完成
2025-07-31 18:22:48.463 | INFO     | src.gui.widgets.target_selection_widget:_save_config:285 | 配置保存成功
2025-07-31 18:22:48.479 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-31 18:22:48.495 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-31 18:22:48.526 | INFO     | src.gui.main_dialogs:_get_template_fields:1872 | 🔧 [P3-修复] ConfigSyncManager未初始化，使用默认字段模板
2025-07-31 18:22:48.526 | INFO     | src.gui.main_dialogs:_init_field_mapping:1859 | 🔧 [P3-修复] 未找到字段模板，使用内置默认字段列表
2025-07-31 18:22:48.588 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-07-31 18:22:48.588 | INFO     | src.gui.main_dialogs:_apply_default_settings:2210 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-07-31 18:22:48.588 | INFO     | src.gui.main_dialogs:_setup_tooltips:2465 | 工具提示设置完成
2025-07-31 18:22:48.588 | INFO     | src.gui.main_dialogs:_setup_shortcuts:2504 | 快捷键设置完成
2025-07-31 18:22:48.588 | INFO     | src.gui.main_dialogs:__init__:77 | 数据导入对话框初始化完成。
2025-07-31 18:22:48.588 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:81 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-07-31 18:22:48.588 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:4993 | 🔧 [P0-修复] ConfigSyncManager已设置到数据导入对话框
2025-07-31 18:22:48.690 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:7512 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-07-31 18:22:55.145 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-31 18:22:56.646 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-31 18:22:56.648 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-07-31 18:22:56.650 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2245 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-07-31 18:22:58.104 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-31 18:22:58.286 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-31 18:22:58.302 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-31 18:22:58.303 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:211 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-31 18:22:58.304 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-31 18:22:58.498 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-31 18:22:58.499 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:222 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-31 18:22:58.502 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-31 18:22:58.502 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-31 18:22:58.504 | INFO     | src.utils.log_config:log_file_operation:252 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-31 18:22:58.604 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-07-31 18:22:58.608 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-31 18:22:58.610 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-07-31 18:22:58.611 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 3行 x 16列
2025-07-31 18:22:58.615 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-07-31 18:22:58.615 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-07-31 18:22:58.622 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 2行 × 16列
2025-07-31 18:22:58.623 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 离休人员工资表 使用智能默认配置
2025-07-31 18:22:58.623 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-07-31 18:22:58.625 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 retired_employees 生成了 21 个字段映射
2025-07-31 18:22:58.625 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 retired_employees 生成字段映射: 21 个字段
2025-07-31 18:22:58.631 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:615 | 完整字段映射保存成功: salary_data_2025_07_retired_employees
2025-07-31 18:22:58.632 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_07_retired_employees 生成标准化字段映射: 21 个字段
2025-07-31 18:22:58.637 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-07-31 18:22:58.638 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet '离休人员工资表' 检测到模板类型: retired_employees
2025-07-31 18:22:58.646 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_07_retired_employees
2025-07-31 18:22:58.651 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_retired_employees (模板: retired_employees)
2025-07-31 18:22:58.709 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | 🔧 [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-07-31 18:22:58.710 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-31 18:22:58.712 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | 🔧 [修复标识] 导入列名映射成功: 18 个字段已映射
2025-07-31 18:22:58.724 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。
2025-07-31 18:22:58.730 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-31 18:22:58.731 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-31 18:22:58.732 | INFO     | src.utils.log_config:log_file_operation:252 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-31 18:22:58.828 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 27列 (列过滤: 否)
2025-07-31 18:22:58.832 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-31 18:22:58.834 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-07-31 18:22:58.835 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 14行 x 27列
2025-07-31 18:22:58.838 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-07-31 18:22:58.839 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-07-31 18:22:58.844 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 13行 × 27列
2025-07-31 18:22:58.845 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 退休人员工资表 使用智能默认配置
2025-07-31 18:22:58.846 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-07-31 18:22:58.847 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 pension_employees 生成了 32 个字段映射
2025-07-31 18:22:58.847 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 pension_employees 生成字段映射: 32 个字段
2025-07-31 18:22:58.852 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:615 | 完整字段映射保存成功: salary_data_2025_07_pension_employees
2025-07-31 18:22:58.856 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_07_pension_employees 生成标准化字段映射: 32 个字段
2025-07-31 18:22:58.862 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-07-31 18:22:58.862 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet '退休人员工资表' 检测到模板类型: pension_employees
2025-07-31 18:22:58.868 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_07_pension_employees
2025-07-31 18:22:58.874 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_pension_employees (模板: pension_employees)
2025-07-31 18:22:58.886 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | 🔧 [修复标识] 导入字段映射加载完成: 32 个映射规则
2025-07-31 18:22:58.887 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-31 18:22:58.889 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | 🔧 [修复标识] 导入列名映射成功: 29 个字段已映射
2025-07-31 18:22:58.904 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。
2025-07-31 18:22:58.905 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-31 18:22:58.906 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-31 18:22:58.907 | INFO     | src.utils.log_config:log_file_operation:252 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-31 18:22:59.043 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-07-31 18:22:59.046 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-31 18:22:59.049 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-07-31 18:22:59.050 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 1397行 x 23列
2025-07-31 18:22:59.055 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-07-31 18:22:59.060 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-07-31 18:22:59.062 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 1396行 × 23列
2025-07-31 18:22:59.063 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 全部在职人员工资表 使用智能默认配置
2025-07-31 18:22:59.064 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-07-31 18:22:59.064 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 active_employees 生成了 28 个字段映射
2025-07-31 18:22:59.066 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 active_employees 生成字段映射: 28 个字段
2025-07-31 18:22:59.074 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:615 | 完整字段映射保存成功: salary_data_2025_07_active_employees
2025-07-31 18:22:59.075 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_07_active_employees 生成标准化字段映射: 28 个字段
2025-07-31 18:22:59.082 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-07-31 18:22:59.083 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet '全部在职人员工资表' 检测到模板类型: active_employees
2025-07-31 18:22:59.091 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_07_active_employees
2025-07-31 18:22:59.098 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_active_employees (模板: active_employees)
2025-07-31 18:22:59.110 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | 🔧 [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-07-31 18:22:59.110 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-31 18:22:59.113 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | 🔧 [修复标识] 导入列名映射成功: 25 个字段已映射
2025-07-31 18:22:59.151 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。
2025-07-31 18:22:59.153 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-31 18:22:59.155 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-31 18:22:59.156 | INFO     | src.utils.log_config:log_file_operation:252 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-31 18:22:59.253 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-07-31 18:22:59.257 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-31 18:22:59.259 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-07-31 18:22:59.260 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 63行 x 21列
2025-07-31 18:22:59.262 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-07-31 18:22:59.264 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-07-31 18:22:59.270 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 62行 × 21列
2025-07-31 18:22:59.271 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 A岗职工 使用智能默认配置
2025-07-31 18:22:59.271 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-07-31 18:22:59.272 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 a_grade_employees 生成了 26 个字段映射
2025-07-31 18:22:59.273 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 a_grade_employees 生成字段映射: 26 个字段
2025-07-31 18:22:59.277 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:615 | 完整字段映射保存成功: salary_data_2025_07_a_grade_employees
2025-07-31 18:22:59.283 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_07_a_grade_employees 生成标准化字段映射: 26 个字段
2025-07-31 18:22:59.287 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet A岗职工 数据处理完成: 62 行
2025-07-31 18:22:59.288 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet 'A岗职工' 检测到模板类型: a_grade_employees
2025-07-31 18:22:59.293 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_07_a_grade_employees
2025-07-31 18:22:59.300 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_a_grade_employees (模板: a_grade_employees)
2025-07-31 18:22:59.312 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | 🔧 [修复标识] 导入字段映射加载完成: 26 个映射规则
2025-07-31 18:22:59.313 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-31 18:22:59.315 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | 🔧 [修复标识] 导入列名映射成功: 23 个字段已映射
2025-07-31 18:22:59.331 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。
2025-07-31 18:22:59.332 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:241 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_07_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_07_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_07_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_07_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-07-31 18:22:59.334 | INFO     | src.gui.main_dialogs:_execute_multi_sheet_import:1479 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_07_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_07_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_07_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_07_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-07', 'data_description': '2025年7月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 07月 > 全部在职人员'}
2025-07-31 18:22:59.337 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:5012 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_07_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_07_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_07_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_07_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-07', 'data_description': '2025年7月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 07月 > 全部在职人员'}
2025-07-31 18:22:59.339 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:5023 | 导入模式: multi_sheet, 目标路径: '工资表 > 2025年 > 07月 > 全部在职人员'
2025-07-31 18:22:59.340 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:5041 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-31 18:23:00.148 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:5129 | 检查是否需要更新导航面板: ['工资表', '2025年', '07月', '全部在职人员']
2025-07-31 18:23:00.149 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:5133 | 检测到工资数据导入，开始刷新导航面板
2025-07-31 18:23:00.152 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:5137 | 使用强制刷新方法
2025-07-31 18:23:00.153 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1539 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-07-31 18:23:00.153 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1547 | 找到工资表节点: 💰 工资表
2025-07-31 18:23:00.166 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 4 个匹配类型 'salary_data' 的表
2025-07-31 18:23:00.167 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1632 | 创建年份节点: 2025年，包含 1 个月份
2025-07-31 18:23:00.168 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1652 | 工资数据导航已强制刷新: 1 个年份, 1 个月份
2025-07-31 18:23:00.168 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1655 | 默认路径: 工资表 > 2025年 > 7月 > 全部在职人员
2025-07-31 18:23:00.169 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1694 | force_refresh_salary_data 执行完成
2025-07-31 18:23:00.170 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:5142 | 将在1500ms后导航到: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-31 18:23:00.170 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:5209 | 尝试导航到新导入的路径: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-31 18:23:00.171 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:5214 | 已成功导航到新导入的路径: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-31 18:23:00.176 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:6734 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '07月', '全部在职人员'] -> salary_data_2025_07_active_employees
2025-07-31 18:23:00.178 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:5302 | [数据流追踪] 开始智能数据显示刷新: salary_data_2025_07_active_employees
2025-07-31 18:23:00.184 | INFO     | src.core.smart_pagination_strategy:__init__:47 | 智能分页策略管理器初始化完成
2025-07-31 18:23:00.187 | INFO     | src.core.smart_pagination_strategy:should_use_pagination:68 | [数据流追踪] 分页决策输入: 总记录数=1396, 页面大小=50, 用户偏好=None
2025-07-31 18:23:00.188 | INFO     | src.core.smart_pagination_strategy:should_use_pagination:93 | [数据流追踪] 分页决策输出: 策略=pagination, 原因=大数据集(1396条)分28页显示, 预期性能=550ms, 决策耗时=1ms
2025-07-31 18:23:00.189 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:5324 | [数据流追踪] 智能分页策略决策: pagination, 原因=大数据集(1396条)分28页显示, 预期性能=550ms
2025-07-31 18:23:00.190 | INFO     | src.gui.prototype.prototype_main_window:_execute_pagination_mode:5398 | [数据流追踪] 执行分页显示模式: salary_data_2025_07_active_employees, 1396条记录
2025-07-31 18:23:00.190 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:6251 | 使用分页模式加载 salary_data_2025_07_active_employees，第1页，每页50条
2025-07-31 18:23:00.192 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:6338 | [数据流追踪] 优化缓存模块未找到: No module named 'cache_optimization_fix'，使用标准缓存机制
2025-07-31 18:23:00.193 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:6380 | 缓存未命中，从数据库加载: salary_data_2025_07_active_employees 第1页
2025-07-31 18:23:00.194 | INFO     | src.gui.prototype.prototype_main_window:_execute_pagination_mode:5416 | [数据流追踪] 分页显示模式: 1396条记录分28页显示
2025-07-31 18:23:00.194 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 开始加载表 salary_data_2025_07_active_employees 第1页数据，每页50条
2025-07-31 18:23:00.195 | INFO     | src.gui.prototype.prototype_main_window:_schedule_safe_navigation:5187 | 同步导航完成: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-31 18:23:00.203 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-07-31 18:23:00.203 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" LIMIT 50 OFFSET 0
2025-07-31 18:23:00.209 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19990089.0', '20161565.0', '20191782.0', '20151515.0', '20181640.0']
2025-07-31 18:23:00.219 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-07-31 18:23:00.220 | INFO     | src.gui.prototype.prototype_main_window:run:156 | 使用排序查询: 0 个排序列
2025-07-31 18:23:00.221 | INFO     | src.gui.prototype.prototype_main_window:run:187 | 原始数据: 50行, 28列
2025-07-31 18:23:00.221 | INFO     | src.gui.prototype.prototype_main_window:run:194 | 开始应用字段映射
2025-07-31 18:23:00.223 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:5823 | 开始统一字段处理: salary_data_2025_07_active_employees, 原始列数: 28
2025-07-31 18:23:00.307 | INFO     | src.modules.format_management.field_registry:__init__:84 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-07-31 18:23:00.308 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-07-31 18:23:00.312 | INFO     | src.modules.format_management.unified_format_manager:_setup_event_listeners:686 | 🎯 [事件驱动] 事件监听器设置完成
2025-07-31 18:23:00.315 | INFO     | src.modules.format_management.unified_format_manager:_sync_to_state_manager:701 | 🎯 [统一状态管理] 状态同步完成
2025-07-31 18:23:00.316 | INFO     | src.modules.format_management.format_config:load_config:279 | 🔧 [格式配置] 配置文件不存在，创建默认配置
2025-07-31 18:23:00.320 | INFO     | src.modules.format_management.format_config:save_config:329 | 🔧 [格式配置] 配置文件保存成功
2025-07-31 18:23:00.323 | INFO     | src.modules.format_management.field_registry:load_mappings:345 | 🏷️ [字段注册] 字段映射加载成功
2025-07-31 18:23:00.323 | INFO     | src.modules.format_management.unified_format_manager:_initialize_system:146 | 🎯 [统一格式管理] 系统初始化完成
2025-07-31 18:23:00.324 | INFO     | src.modules.format_management.unified_format_manager:__init__:130 | 🎯 [统一格式管理] 统一格式管理器初始化完成（新架构版本）
2025-07-31 18:23:00.338 | INFO     | src.modules.format_management.format_renderer:render_dataframe:141 | 🎯 [格式渲染] 已按display_order排列字段: 28个字段
2025-07-31 18:23:00.339 | INFO     | src.modules.format_management.format_renderer:render_dataframe:143 | 🎨 [格式渲染] DataFrame格式化完成: salary_data_2025_07_active_employees, 行数: 50, 列数: 28
2025-07-31 18:23:00.340 | INFO     | src.modules.format_management.unified_format_manager:format_data:363 | 🎯 [统一格式管理] 数据格式化完成: salary_data_2025_07_active_employees, 行数: 50, 列数: 28
2025-07-31 18:23:00.347 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:23:00.349 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:5876 | 🔧 [字段处理] 统一字段处理完成并缓存: 24个字段
2025-07-31 18:23:00.349 | INFO     | src.gui.prototype.prototype_main_window:run:204 | PaginationWorker - 字段映射成功: 28 -> 24列
2025-07-31 18:23:00.350 | INFO     | src.gui.prototype.prototype_main_window:run:218 | 字段映射成功: 24列
2025-07-31 18:23:00.352 | INFO     | src.gui.prototype.prototype_main_window:run:244 | 最终数据: 50行, 24列, 总记录数: 1396
2025-07-31 18:23:00.352 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:6411 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-07-31 18:23:00.353 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:6445 | 🚀 [性能缓存] 数据已缓存: salary_data_2025_07_active_employees 第1页
2025-07-31 18:23:00.359 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第2页, 每页50条
2025-07-31 18:23:00.361 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:6474 | 🧹 [异步分页] 使用重构后的统一格式化结果: 50行, 24列
2025-07-31 18:23:00.364 | INFO     | src.gui.prototype.prototype_main_window:set_data:728 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-31 18:23:00.369 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:23:00.369 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第2页数据: 50 行，总计1396行
2025-07-31 18:23:00.384 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089, 薪资=2375
2025-07-31 18:23:00.386 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1696
2025-07-31 18:23:00.387 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:4558 | 最大可见行数已更新: 1000 -> 50
2025-07-31 18:23:00.387 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-31 18:23:00.406 | INFO     | src.modules.format_management.master_format_manager:_load_format_config:475 | 格式化配置加载成功: C:\test\salary_changes\salary_changes\state\format_config.json
2025-07-31 18:23:00.407 | INFO     | src.modules.format_management.field_registry:__init__:84 | 🏷️ [字段注册] 字段注册系统初始化: C:\test\salary_changes\salary_changes\state\data\field_mappings.json
2025-07-31 18:23:00.410 | INFO     | src.modules.format_management.field_registry:load_mappings:345 | 🏷️ [字段注册] 字段映射加载成功
2025-07-31 18:23:00.412 | INFO     | src.modules.format_management.format_config:load_config:297 | 🔧 [格式配置] 配置文件加载成功
2025-07-31 18:23:00.412 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-07-31 18:23:00.414 | INFO     | src.modules.format_management.master_format_manager:_initialize_unified_format_system:182 | 🔧 [新系统] 统一格式管理系统组件初始化完成
2025-07-31 18:23:00.414 | INFO     | src.modules.format_management.master_format_manager:clear_cache:453 | 🎯 [统一格式化] 格式化缓存已清理
2025-07-31 18:23:00.415 | INFO     | src.modules.format_management.master_format_manager:__init__:156 | 🎯 [统一格式化] 主格式化管理器已初始化（新系统）
2025-07-31 18:23:00.426 | INFO     | src.modules.format_management.format_renderer:render_dataframe:143 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-07-31 18:23:00.427 | WARNING  | src.modules.format_management.master_format_manager:_verify_formatting_completeness:694 | 🔧 [格式化验证] 以下字段格式化不完整: ['人员类别代码', '车补', '补发', '借支', '代扣代存养老保险', '月份']
2025-07-31 18:23:00.428 | WARNING  | src.modules.format_management.master_format_manager:format_table_data:226 | 🎯 [统一格式化] 格式化验证未通过，不标记: active_employees
2025-07-31 18:23:00.428 | INFO     | src.modules.format_management.master_format_manager:format_table_data:232 | 🎯 [统一格式化] 数据格式化完成: active_employees
2025-07-31 18:23:00.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2517 | 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-31 18:23:00.440 | INFO     | src.modules.format_management.format_config:load_config:297 | 🔧 [格式配置] 配置文件加载成功
2025-07-31 18:23:00.459 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-07-31 18:23:00.460 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-07-31 18:23:00.471 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时10.4ms, 平均每行0.21ms
2025-07-31 18:23:00.481 | INFO     | src.core.performance_metrics_collector:__init__:74 | 性能度量收集器初始化完成，存储路径: logs\performance_metrics.json
2025-07-31 18:23:00.482 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=10.4ms, 策略=small_dataset
2025-07-31 18:23:00.482 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-07-31 18:23:00.483 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2598 | [数据流追踪] 优化渲染完成: 50行 x 24列, 策略=small_dataset, 耗时=10.4ms, 性能评级=excellent
2025-07-31 18:23:00.488 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 19990089
2025-07-31 18:23:00.489 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 20161565
2025-07-31 18:23:00.489 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 20191782
2025-07-31 18:23:00.490 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 20151515
2025-07-31 18:23:00.491 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 20181640
2025-07-31 18:23:00.492 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-07-31 18:23:00.492 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375
2025-07-31 18:23:00.493 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696
2025-07-31 18:23:00.494 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427
2025-07-31 18:23:00.495 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175
2025-07-31 18:23:00.501 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582
2025-07-31 18:23:00.502 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:472 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-31 18:23:00.503 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 50 行, 24 列
2025-07-31 18:23:00.505 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:409 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_07_active_employees
2025-07-31 18:23:00.506 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:440 | 🔧 [新架构] 为表格 salary_data_2025_07_active_employees 重新加载 28 个字段映射
2025-07-31 18:23:00.507 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2678 | 表格数据设置完成: 50 行, 耗时: 120.8ms
2025-07-31 18:23:00.507 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7115 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-31 18:23:00.508 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7128 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-31 18:23:00.509 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1632 | 🆕 [列宽保存] 未找到表格 salary_data_2025_07_active_employees 的列宽配置
2025-07-31 18:23:00.516 | INFO     | src.gui.widgets.pagination_widget:set_total_records:490 | 总记录数设置为: 50
2025-07-31 18:23:00.516 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:6493 | 🔍 [调试-分页] 即将调用set_pagination_state: 第1页, 记录1-50
2025-07-31 18:23:00.517 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3721 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-07-31 18:23:00.518 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3722 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 1396, 'start_record': 1, 'end_record': 50}
2025-07-31 18:23:00.518 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3723 | 🔍 [表格调试] 当前表格行数: 50
2025-07-31 18:23:00.519 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3724 | 🔍 [表格调试] 当前表格列数: 24
2025-07-31 18:23:00.520 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3726 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-07-31 18:23:00.521 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3752 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-07-31 18:23:00.522 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-07-31 18:23:00.522 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:3831 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-07-31 18:23:00.529 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:6495 | 🔍 [调试-分页] set_pagination_state调用完成
2025-07-31 18:23:00.530 | INFO     | src.gui.widgets.pagination_widget:set_total_records:490 | 总记录数设置为: 1396
2025-07-31 18:23:00.534 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:6552 | 🆕 [新架构] 分页数据加载完成，已完成渐进式状态迁移
2025-07-31 18:23:01.154 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:7512 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-07-31 18:23:09.052 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4275 | 🔧 [分页请求] 第2页, 表: salary_data_2025_07_active_employees, 上下文已设置
2025-07-31 18:23:09.068 | INFO     | src.core.request_deduplication_manager:__init__:57 | 请求去重管理器初始化完成，默认TTL: 0.3秒
2025-07-31 18:23:09.068 | INFO     | src.core.request_deduplication_manager:__init__:278 | 智能去重管理器初始化完成
2025-07-31 18:23:09.068 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4305 | [数据流追踪] 分页请求通过去重检查: salary_data_2025_07_active_employees, 第2页, pagination
2025-07-31 18:23:09.068 | INFO     | src.services.table_data_service:load_table_data:347 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 2
2025-07-31 18:23:09.068 | INFO     | src.core.unified_data_request_manager:request_table_data:203 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: initial_load
2025-07-31 18:23:09.068 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第2页, 每页50条, 排序=0列
2025-07-31 18:23:09.068 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" LIMIT 50 OFFSET 50
2025-07-31 18:23:09.083 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['20171604.0', '20181638.0', '19930191.0', '20181669.0', '20191722.0']
2025-07-31 18:23:09.083 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第2页数据（含排序）: 50 行，总计1396行
2025-07-31 18:23:09.083 | INFO     | src.core.unified_data_request_manager:request_table_data:252 | 数据请求处理完成: 28字段, 50行, 耗时15.7ms
2025-07-31 18:23:09.083 | INFO     | temp.format_manager_singleton_fix:__init__:120 | [单例修复] 主格式化管理器单例初始化完成
2025-07-31 18:23:09.083 | INFO     | src.services.table_data_service:load_table_data:434 | [性能优化] 使用单例格式化管理器，避免重复初始化
2025-07-31 18:23:09.083 | INFO     | src.services.table_data_service:load_table_data:452 | [统一格式化] 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-31 18:23:09.083 | INFO     | src.services.table_data_service:load_table_data:489 | [修复数据发布] 数据加载成功，发布更新事件: salary_data_2025_07_active_employees, 50行
2025-07-31 18:23:09.083 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3317 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-31 18:23:09.083 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3342 | 数据内容: 50行 x 28列
2025-07-31 18:23:09.083 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3368 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_07_active_employees, 50行
2025-07-31 18:23:09.102 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:23:09.102 | INFO     | src.gui.prototype.prototype_main_window:set_data:728 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-31 18:23:09.102 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:23:09.116 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=20171604.0, 薪资=1427.0
2025-07-31 18:23:09.116 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20181638.0, 薪资=1515.0
2025-07-31 18:23:09.116 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:4558 | 最大可见行数已更新: 50 -> 50
2025-07-31 18:23:09.116 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-31 18:23:09.130 | INFO     | src.modules.format_management.format_renderer:render_dataframe:143 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-07-31 18:23:09.130 | WARNING  | src.modules.format_management.master_format_manager:_verify_formatting_completeness:694 | 🔧 [格式化验证] 以下字段格式化不完整: ['人员类别代码', '车补', '补发', '借支', '月份']
2025-07-31 18:23:09.130 | WARNING  | src.modules.format_management.master_format_manager:format_table_data:226 | 🎯 [统一格式化] 格式化验证未通过，不标记: active_employees
2025-07-31 18:23:09.130 | INFO     | src.modules.format_management.master_format_manager:format_table_data:232 | 🎯 [统一格式化] 数据格式化完成: active_employees
2025-07-31 18:23:09.130 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2517 | 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-31 18:23:09.146 | WARNING  | src.modules.format_management.master_format_manager:_format_department_code_value:386 | 🔧 [部门代码格式化] 格式化失败 -: could not convert string to float: '-'
2025-07-31 18:23:09.146 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-07-31 18:23:09.146 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-07-31 18:23:09.162 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时15.7ms, 平均每行0.31ms
2025-07-31 18:23:09.162 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=15.7ms, 策略=small_dataset
2025-07-31 18:23:09.162 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-07-31 18:23:09.162 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2598 | [数据流追踪] 优化渲染完成: 50行 x 24列, 策略=small_dataset, 耗时=15.7ms, 性能评级=excellent
2025-07-31 18:23:09.162 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 20171604
2025-07-31 18:23:09.162 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 20181638
2025-07-31 18:23:09.162 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 19930191
2025-07-31 18:23:09.162 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 20181669
2025-07-31 18:23:09.162 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 20191722
2025-07-31 18:23:09.162 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-07-31 18:23:09.162 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[0]: 工号=20171604, 薪资=1427
2025-07-31 18:23:09.162 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[1]: 工号=20181638, 薪资=1515
2025-07-31 18:23:09.177 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[2]: 工号=19930191, 薪资=3391
2025-07-31 18:23:09.177 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[3]: 工号=20181669, 薪资=1515
2025-07-31 18:23:09.177 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[4]: 工号=20191722, 薪资=1515
2025-07-31 18:23:09.177 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:472 | 可见行数据顺序: ['20171604', '20181638', '19930191', '20181669', '20191722']
2025-07-31 18:23:09.177 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 50 行, 24 列
2025-07-31 18:23:09.177 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2678 | 表格数据设置完成: 50 行, 耗时: 75.4ms
2025-07-31 18:23:09.177 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7115 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-31 18:23:09.177 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7128 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-31 18:23:09.177 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1632 | 🆕 [列宽保存] 未找到表格 salary_data_2025_07_active_employees 的列宽配置
2025-07-31 18:23:09.177 | INFO     | src.gui.widgets.pagination_widget:set_total_records:490 | 总记录数设置为: 50
2025-07-31 18:23:09.198 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3400 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-07-31 18:23:09.199 | INFO     | src.gui.widgets.pagination_widget:set_total_records:490 | 总记录数设置为: 1396
2025-07-31 18:23:09.200 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3414 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-07-31 18:23:09.201 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3433 | 🔧 [分页修复] 分页状态: 当前页=2, 总页数=28, 总记录数=1396
2025-07-31 18:23:09.201 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3439 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=True
2025-07-31 18:23:09.202 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3450 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第2页, 记录51-100
2025-07-31 18:23:09.202 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3721 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-07-31 18:23:09.203 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3722 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 2, 'page_size': 50, 'total_records': 1396, 'start_record': 51, 'end_record': 100}
2025-07-31 18:23:09.204 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3723 | 🔍 [表格调试] 当前表格行数: 50
2025-07-31 18:23:09.205 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3724 | 🔍 [表格调试] 当前表格列数: 24
2025-07-31 18:23:09.206 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3726 | 🔧 [分页行号修复] 接收分页状态: 第2页, 记录51-100
2025-07-31 18:23:09.212 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3752 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-07-31 18:23:09.213 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 51, 共 50 行
2025-07-31 18:23:09.215 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:3831 | 🔧 [分页行号修复] 强制更新完成: 起始记录51, 共50行
2025-07-31 18:23:09.215 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3452 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-07-31 18:23:09.216 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3466 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-07-31 18:23:09.217 | INFO     | src.services.table_data_service:load_table_data:510 | [修复数据发布] 数据更新事件已发布
2025-07-31 18:23:09.218 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4320 | 🚫 [用户要求] 不显示加载条，直接处理数据: salary_data_2025_07_active_employees 第2页
2025-07-31 18:23:09.219 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4323 | 🔧 [根本修复] 分页数据获取成功: 第2页, 50行
2025-07-31 18:23:09.220 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4329 | 🔧 [根本修复] 获取数据的前3个工号: ['无工号']
2025-07-31 18:23:09.220 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4337 | [数据流追踪] 分页数据请求成功: 第2页, 50行
2025-07-31 18:23:09.221 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4344 | 🔧 [关键修复] 分页处理标志已清除，允许UI更新: 第2页
2025-07-31 18:23:09.228 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4349 | 🧹 [智能修复] 数据返回完成，缓存状态: False, 第2页
2025-07-31 18:23:09.229 | INFO     | src.gui.widgets.pagination_widget:set_total_records:490 | 总记录数设置为: 1396
2025-07-31 18:23:09.230 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4364 | 🔧 [简化分页处理] 分页处理完成，等待事件机制更新UI: 第2页
2025-07-31 18:23:09.231 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4414 | 🚫 [根本修复] 分页处理完成，标志已清除: 第2页
2025-07-31 18:23:09.231 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4275 | 🔧 [分页请求] 第2页, 表: salary_data_2025_07_active_employees, 上下文已设置
2025-07-31 18:23:09.232 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4305 | [数据流追踪] 分页请求通过去重检查: salary_data_2025_07_active_employees, 第2页, pagination
2025-07-31 18:23:09.233 | INFO     | src.services.table_data_service:load_table_data:347 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 2
2025-07-31 18:23:09.234 | INFO     | src.services.table_data_service:load_table_data:370 | [缓存命中] 使用缓存数据: salary_data_2025_07_active_employees 第2页
2025-07-31 18:23:09.235 | INFO     | src.services.table_data_service:load_table_data:376 | [根本修复] 缓存命中，发布数据更新事件: salary_data_2025_07_active_employees, 50行
2025-07-31 18:23:09.235 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3317 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-31 18:23:09.242 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3342 | 数据内容: 50行 x 28列
2025-07-31 18:23:09.243 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3368 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_07_active_employees, 50行
2025-07-31 18:23:09.245 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:23:09.246 | INFO     | src.gui.prototype.prototype_main_window:set_data:728 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-31 18:23:09.248 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:23:09.253 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=20171604.0, 薪资=1427.0
2025-07-31 18:23:09.254 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20181638.0, 薪资=1515.0
2025-07-31 18:23:09.255 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:4558 | 最大可见行数已更新: 50 -> 50
2025-07-31 18:23:09.256 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-31 18:23:09.265 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2517 | 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-31 18:23:09.275 | WARNING  | src.modules.format_management.master_format_manager:_format_department_code_value:386 | 🔧 [部门代码格式化] 格式化失败 -: could not convert string to float: '-'
2025-07-31 18:23:09.284 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-07-31 18:23:09.284 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-07-31 18:23:09.294 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时9.7ms, 平均每行0.19ms
2025-07-31 18:23:09.294 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=9.7ms, 策略=small_dataset
2025-07-31 18:23:09.295 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-07-31 18:23:09.296 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2598 | [数据流追踪] 优化渲染完成: 50行 x 24列, 策略=small_dataset, 耗时=9.7ms, 性能评级=excellent
2025-07-31 18:23:09.298 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 20171604
2025-07-31 18:23:09.301 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 20181638
2025-07-31 18:23:09.302 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 19930191
2025-07-31 18:23:09.303 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 20181669
2025-07-31 18:23:09.304 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 20191722
2025-07-31 18:23:09.305 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-07-31 18:23:09.306 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[0]: 工号=20171604, 薪资=1427
2025-07-31 18:23:09.306 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[1]: 工号=20181638, 薪资=1515
2025-07-31 18:23:09.307 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[2]: 工号=19930191, 薪资=3391
2025-07-31 18:23:09.308 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[3]: 工号=20181669, 薪资=1515
2025-07-31 18:23:09.309 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[4]: 工号=20191722, 薪资=1515
2025-07-31 18:23:09.314 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:472 | 可见行数据顺序: ['20171604', '20181638', '19930191', '20181669', '20191722']
2025-07-31 18:23:09.315 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 50 行, 24 列
2025-07-31 18:23:09.317 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2678 | 表格数据设置完成: 50 行, 耗时: 65.1ms
2025-07-31 18:23:09.317 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7115 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-31 18:23:09.318 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7128 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-31 18:23:09.319 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1632 | 🆕 [列宽保存] 未找到表格 salary_data_2025_07_active_employees 的列宽配置
2025-07-31 18:23:09.321 | INFO     | src.gui.widgets.pagination_widget:set_total_records:490 | 总记录数设置为: 50
2025-07-31 18:23:09.321 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3400 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-07-31 18:23:09.322 | INFO     | src.gui.widgets.pagination_widget:set_total_records:490 | 总记录数设置为: 1396
2025-07-31 18:23:09.323 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3414 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-07-31 18:23:09.329 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3433 | 🔧 [分页修复] 分页状态: 当前页=2, 总页数=28, 总记录数=1396
2025-07-31 18:23:09.329 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3439 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=True
2025-07-31 18:23:09.330 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3450 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第2页, 记录51-100
2025-07-31 18:23:09.331 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3721 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-07-31 18:23:09.332 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3722 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 2, 'page_size': 50, 'total_records': 1396, 'start_record': 51, 'end_record': 100}
2025-07-31 18:23:09.333 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3723 | 🔍 [表格调试] 当前表格行数: 50
2025-07-31 18:23:09.334 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3724 | 🔍 [表格调试] 当前表格列数: 24
2025-07-31 18:23:09.334 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3726 | 🔧 [分页行号修复] 接收分页状态: 第2页, 记录51-100
2025-07-31 18:23:09.335 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3752 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-07-31 18:23:09.336 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 51, 共 50 行
2025-07-31 18:23:09.337 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:3831 | 🔧 [分页行号修复] 强制更新完成: 起始记录51, 共50行
2025-07-31 18:23:09.344 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3452 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-07-31 18:23:09.344 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3466 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-07-31 18:23:09.345 | INFO     | src.services.table_data_service:load_table_data:398 | [根本修复] 缓存数据更新事件已发布，UI应该能正常更新
2025-07-31 18:23:09.346 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4320 | 🚫 [用户要求] 不显示加载条，直接处理数据: salary_data_2025_07_active_employees 第2页
2025-07-31 18:23:09.347 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4323 | 🔧 [根本修复] 分页数据获取成功: 第2页, 50行
2025-07-31 18:23:09.348 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4329 | 🔧 [根本修复] 获取数据的前3个工号: ['无工号']
2025-07-31 18:23:09.348 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4337 | [数据流追踪] 分页数据请求成功: 第2页, 50行
2025-07-31 18:23:09.349 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4344 | 🔧 [关键修复] 分页处理标志已清除，允许UI更新: 第2页
2025-07-31 18:23:09.350 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4349 | 🧹 [智能修复] 数据返回完成，缓存状态: True, 第2页
2025-07-31 18:23:09.351 | INFO     | src.gui.widgets.pagination_widget:set_total_records:490 | 总记录数设置为: 1396
2025-07-31 18:23:09.352 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4364 | 🔧 [简化分页处理] 分页处理完成，等待事件机制更新UI: 第2页
2025-07-31 18:23:09.358 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4414 | 🚫 [根本修复] 分页处理完成，标志已清除: 第2页
2025-07-31 18:23:09.359 | INFO     | src.gui.widgets.pagination_widget:set_current_page:545 | 页码切换到: 2
2025-07-31 18:23:12.871 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表']
2025-07-31 18:23:12.871 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:6034 | 导航变化: 工资表 > 2025年
2025-07-31 18:23:12.871 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8517 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_07_active_employees -> None
2025-07-31 18:23:12.871 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8533 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-07-31 18:23:12.871 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:5941 | 🔧 [缓存清理] 清理了所有 1 个字段处理缓存条目
2025-07-31 18:23:12.871 | WARNING  | src.gui.prototype.prototype_main_window:set_data:688 | 尝试设置空数据，将显示提示信息。
2025-07-31 18:23:12.871 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6895 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-07-31 18:23:12.871 | WARNING  | src.gui.prototype.prototype_main_window:set_data:672 | 🔧 [P0-2] 检测到递归设置数据，跳过以防止表头重影
2025-07-31 18:23:12.887 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6910 | 已显示标准空表格，表头数量: 22
2025-07-31 18:23:12.887 | INFO     | src.gui.widgets.pagination_widget:reset:595 | 分页状态已重置
2025-07-31 18:23:12.887 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6895 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-07-31 18:23:12.887 | WARNING  | src.gui.prototype.prototype_main_window:set_data:708 | 尝试设置空数据，将显示提示信息。
2025-07-31 18:23:12.887 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6895 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-07-31 18:23:12.887 | WARNING  | src.gui.prototype.prototype_main_window:set_data:672 | 🔧 [P0-2] 检测到递归设置数据，跳过以防止表头重影
2025-07-31 18:23:12.887 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6910 | 已显示标准空表格，表头数量: 22
2025-07-31 18:23:12.887 | INFO     | src.gui.widgets.pagination_widget:reset:595 | 分页状态已重置
2025-07-31 18:23:12.887 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6910 | 已显示标准空表格，表头数量: 22
2025-07-31 18:23:12.887 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:882 | 导航选择: 工资表 > 2025年
2025-07-31 18:23:14.090 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 7月', '工资表']
2025-07-31 18:23:14.090 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:6034 | 导航变化: 工资表 > 2025年 > 7月
2025-07-31 18:23:14.106 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8517 | 🚫 [用户要求] 表格切换不显示加载条: None -> None
2025-07-31 18:23:14.106 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8533 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-07-31 18:23:14.106 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:5941 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-07-31 18:23:14.106 | WARNING  | src.gui.prototype.prototype_main_window:set_data:688 | 尝试设置空数据，将显示提示信息。
2025-07-31 18:23:14.106 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6895 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-07-31 18:23:14.106 | WARNING  | src.gui.prototype.prototype_main_window:set_data:672 | 🔧 [P0-2] 检测到递归设置数据，跳过以防止表头重影
2025-07-31 18:23:14.106 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6910 | 已显示标准空表格，表头数量: 22
2025-07-31 18:23:14.106 | INFO     | src.gui.widgets.pagination_widget:reset:595 | 分页状态已重置
2025-07-31 18:23:14.106 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6895 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-07-31 18:23:14.106 | WARNING  | src.gui.prototype.prototype_main_window:set_data:708 | 尝试设置空数据，将显示提示信息。
2025-07-31 18:23:14.106 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6895 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-07-31 18:23:14.106 | WARNING  | src.gui.prototype.prototype_main_window:set_data:672 | 🔧 [P0-2] 检测到递归设置数据，跳过以防止表头重影
2025-07-31 18:23:14.106 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6910 | 已显示标准空表格，表头数量: 22
2025-07-31 18:23:14.106 | INFO     | src.gui.widgets.pagination_widget:reset:595 | 分页状态已重置
2025-07-31 18:23:14.106 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6910 | 已显示标准空表格，表头数量: 22
2025-07-31 18:23:14.106 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:882 | 导航选择: 工资表 > 2025年 > 7月
2025-07-31 18:23:15.668 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 7月', '工资表 > 2025年 > 7月 > 全部在职人员', '工资表']
2025-07-31 18:23:15.668 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:6034 | 导航变化: 工资表 > 2025年 > 7月 > 全部在职人员
2025-07-31 18:23:15.668 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8517 | 🚫 [用户要求] 表格切换不显示加载条: None -> None
2025-07-31 18:23:15.668 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8533 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-07-31 18:23:15.668 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:5941 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-07-31 18:23:15.684 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:6734 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '7月', '全部在职人员'] -> salary_data_2025_07_active_employees
2025-07-31 18:23:15.684 | INFO     | src.gui.widgets.pagination_widget:reset:595 | 分页状态已重置
2025-07-31 18:23:15.684 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_07_active_employees 的缓存
2025-07-31 18:23:15.684 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:8985 | 已注册 4 个表格到表头管理器
2025-07-31 18:23:15.684 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1084 | 🔧 [P1-3] 开始智能表头重影检测，共 4 个表格
2025-07-31 18:23:15.684 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1157 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-07-31 18:23:15.684 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1158 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-07-31 18:23:15.684 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:6115 | 🆕 使用新架构加载数据: salary_data_2025_07_active_employees（通过事件系统）
2025-07-31 18:23:15.684 | INFO     | src.services.table_data_service:load_table_data:347 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-31 18:23:15.684 | INFO     | src.core.unified_data_request_manager:request_table_data:203 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: initial_load
2025-07-31 18:23:15.684 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-07-31 18:23:15.684 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" LIMIT 50 OFFSET 0
2025-07-31 18:23:15.699 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19990089.0', '20161565.0', '20191782.0', '20151515.0', '20181640.0']
2025-07-31 18:23:15.699 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-07-31 18:23:15.699 | INFO     | src.core.unified_data_request_manager:request_table_data:252 | 数据请求处理完成: 28字段, 50行, 耗时15.7ms
2025-07-31 18:23:15.699 | INFO     | src.services.table_data_service:load_table_data:434 | [性能优化] 使用单例格式化管理器，避免重复初始化
2025-07-31 18:23:15.699 | INFO     | src.services.table_data_service:load_table_data:452 | [统一格式化] 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-31 18:23:15.699 | INFO     | src.services.table_data_service:load_table_data:489 | [修复数据发布] 数据加载成功，发布更新事件: salary_data_2025_07_active_employees, 50行
2025-07-31 18:23:15.699 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3317 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-31 18:23:15.715 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3342 | 数据内容: 50行 x 28列
2025-07-31 18:23:15.715 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3368 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_07_active_employees, 50行
2025-07-31 18:23:15.715 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:23:15.715 | INFO     | src.gui.prototype.prototype_main_window:set_data:728 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-31 18:23:15.715 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:23:15.715 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089.0, 薪资=2375.0
2025-07-31 18:23:15.715 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565.0, 薪资=1696.0
2025-07-31 18:23:15.715 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:4558 | 最大可见行数已更新: 50 -> 50
2025-07-31 18:23:15.715 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-31 18:23:15.746 | INFO     | src.modules.format_management.format_renderer:render_dataframe:143 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-07-31 18:23:15.746 | WARNING  | src.modules.format_management.master_format_manager:_verify_formatting_completeness:694 | 🔧 [格式化验证] 以下字段格式化不完整: ['人员类别代码', '车补', '补发', '借支', '代扣代存养老保险', '月份']
2025-07-31 18:23:15.746 | WARNING  | src.modules.format_management.master_format_manager:format_table_data:226 | 🎯 [统一格式化] 格式化验证未通过，不标记: active_employees
2025-07-31 18:23:15.746 | INFO     | src.modules.format_management.master_format_manager:format_table_data:232 | 🎯 [统一格式化] 数据格式化完成: active_employees
2025-07-31 18:23:15.746 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2517 | 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-31 18:23:15.762 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-07-31 18:23:15.762 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-07-31 18:23:15.779 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时17.1ms, 平均每行0.34ms
2025-07-31 18:23:15.779 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=17.1ms, 策略=small_dataset
2025-07-31 18:23:15.779 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-07-31 18:23:15.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2598 | [数据流追踪] 优化渲染完成: 50行 x 24列, 策略=small_dataset, 耗时=17.1ms, 性能评级=excellent
2025-07-31 18:23:15.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 19990089
2025-07-31 18:23:15.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 20161565
2025-07-31 18:23:15.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 20191782
2025-07-31 18:23:15.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 20151515
2025-07-31 18:23:15.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 20181640
2025-07-31 18:23:15.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-07-31 18:23:15.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375
2025-07-31 18:23:15.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696
2025-07-31 18:23:15.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427
2025-07-31 18:23:15.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175
2025-07-31 18:23:15.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582
2025-07-31 18:23:15.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:472 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-31 18:23:15.793 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 50 行, 24 列
2025-07-31 18:23:15.793 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2678 | 表格数据设置完成: 50 行, 耗时: 78.4ms
2025-07-31 18:23:15.793 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7115 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-31 18:23:15.793 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7128 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-31 18:23:15.793 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1632 | 🆕 [列宽保存] 未找到表格 salary_data_2025_07_active_employees 的列宽配置
2025-07-31 18:23:15.793 | INFO     | src.gui.widgets.pagination_widget:set_total_records:490 | 总记录数设置为: 50
2025-07-31 18:23:15.806 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3400 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-07-31 18:23:15.806 | INFO     | src.gui.widgets.pagination_widget:set_total_records:490 | 总记录数设置为: 1396
2025-07-31 18:23:15.807 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3414 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-07-31 18:23:15.807 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3433 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=28, 总记录数=1396
2025-07-31 18:23:15.809 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3439 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=False
2025-07-31 18:23:15.814 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3450 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-50
2025-07-31 18:23:15.815 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3721 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-07-31 18:23:15.816 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3722 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 1396, 'start_record': 1, 'end_record': 50}
2025-07-31 18:23:15.817 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3723 | 🔍 [表格调试] 当前表格行数: 50
2025-07-31 18:23:15.817 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3724 | 🔍 [表格调试] 当前表格列数: 24
2025-07-31 18:23:15.818 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3726 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-07-31 18:23:15.819 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3752 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-07-31 18:23:15.820 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-07-31 18:23:15.820 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:3831 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-07-31 18:23:15.820 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3452 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-07-31 18:23:15.875 | INFO     | src.gui.table_header_manager:cleanup_callback:131 | 🔧 [P1-1] 表格 table_3_2604896062496 已自动清理（弱引用回调）
2025-07-31 18:23:15.875 | INFO     | src.gui.table_header_manager:cleanup_callback:131 | 🔧 [P1-1] 表格 table_2_2604896063072 已自动清理（弱引用回调）
2025-07-31 18:23:15.895 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-07-31 18:23:15.896 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:8793 | ✅ [新架构] 组件状态一致性验证通过
2025-07-31 18:23:15.897 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:8756 | 🆕 [新架构] 导航迁移完成
2025-07-31 18:23:15.898 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3466 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-07-31 18:23:15.902 | INFO     | src.services.table_data_service:load_table_data:510 | [修复数据发布] 数据更新事件已发布
2025-07-31 18:23:15.903 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:6120 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-07-31 18:23:15.903 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:882 | 导航选择: 工资表 > 2025年 > 7月 > 全部在职人员
2025-07-31 18:23:17.387 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 7月 > 全部在职人员', '工资表 > 2025年 > 7月', '工资表 > 2025年 > 7月 > 退休人员', '工资表']
2025-07-31 18:23:17.387 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:6034 | 导航变化: 工资表 > 2025年 > 7月 > 退休人员
2025-07-31 18:23:17.387 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8517 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_07_active_employees -> None
2025-07-31 18:23:17.387 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8533 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-07-31 18:23:17.387 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:5941 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-07-31 18:23:17.387 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:6734 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '7月', '退休人员'] -> salary_data_2025_07_pension_employees
2025-07-31 18:23:17.387 | INFO     | src.gui.widgets.pagination_widget:reset:595 | 分页状态已重置
2025-07-31 18:23:17.387 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_07_pension_employees 的缓存
2025-07-31 18:23:17.387 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:8985 | 已注册 2 个表格到表头管理器
2025-07-31 18:23:17.387 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1084 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-07-31 18:23:17.387 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1157 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-07-31 18:23:17.387 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1158 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-07-31 18:23:17.387 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:6115 | 🆕 使用新架构加载数据: salary_data_2025_07_pension_employees（通过事件系统）
2025-07-31 18:23:17.387 | INFO     | src.services.table_data_service:load_table_data:347 | 加载表格数据: salary_data_2025_07_pension_employees, 页码: 1
2025-07-31 18:23:17.387 | INFO     | src.core.unified_data_request_manager:request_table_data:203 | 开始处理数据请求: salary_data_2025_07_pension_employees, 类型: initial_load
2025-07-31 18:23:17.387 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_pension_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-07-31 18:23:17.403 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_pension_employees" LIMIT 50 OFFSET 0
2025-07-31 18:23:17.403 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19709165.0', '19981259.0', '19721294.0', '19841258.0', '19499098.0']
2025-07-31 18:23:17.403 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_pension_employees 获取第1页数据（含排序）: 13 行，总计13行
2025-07-31 18:23:17.403 | INFO     | src.core.unified_data_request_manager:request_table_data:252 | 数据请求处理完成: 32字段, 13行, 耗时15.7ms
2025-07-31 18:23:17.403 | INFO     | src.services.table_data_service:load_table_data:434 | [性能优化] 使用单例格式化管理器，避免重复初始化
2025-07-31 18:23:17.403 | INFO     | src.services.table_data_service:load_table_data:452 | [统一格式化] 数据格式化完成: salary_data_2025_07_pension_employees, 类型: retired_employees
2025-07-31 18:23:17.419 | INFO     | src.services.table_data_service:load_table_data:489 | [修复数据发布] 数据加载成功，发布更新事件: salary_data_2025_07_pension_employees, 13行
2025-07-31 18:23:17.419 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3317 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_pension_employees
2025-07-31 18:23:17.419 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3342 | 数据内容: 13行 x 32列
2025-07-31 18:23:17.419 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3368 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_07_pension_employees, 13行
2025-07-31 18:23:17.419 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_pension_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:23:17.419 | INFO     | src.gui.prototype.prototype_main_window:set_data:728 | 通过公共接口设置新的表格数据(DataFrame): 13 行
2025-07-31 18:23:17.419 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_pension_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:23:17.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=未知0, 薪资=N/A
2025-07-31 18:23:17.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=未知1, 薪资=N/A
2025-07-31 18:23:17.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:4558 | 最大可见行数已更新: 50 -> 13
2025-07-31 18:23:17.434 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4611 | 根据数据量(13)自动调整最大可见行数为: 13
2025-07-31 18:23:17.449 | INFO     | src.modules.format_management.format_renderer:render_dataframe:143 | 🎨 [格式渲染] DataFrame格式化完成: retired_employees, 行数: 13, 列数: 28
2025-07-31 18:23:17.466 | WARNING  | src.modules.format_management.master_format_manager:_verify_formatting_completeness:694 | 🔧 [格式化验证] 以下字段格式化不完整: ['人员类别代码', '补发', '借支', '月份']
2025-07-31 18:23:17.466 | WARNING  | src.modules.format_management.master_format_manager:format_table_data:226 | 🎯 [统一格式化] 格式化验证未通过，不标记: retired_employees
2025-07-31 18:23:17.466 | INFO     | src.modules.format_management.master_format_manager:format_table_data:232 | 🎯 [统一格式化] 数据格式化完成: retired_employees
2025-07-31 18:23:17.466 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2517 | 数据格式化完成: salary_data_2025_07_pension_employees, 类型: retired_employees
2025-07-31 18:23:17.466 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 13行数据
2025-07-31 18:23:17.466 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 13行 x 28列
2025-07-31 18:23:17.482 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时16.7ms, 平均每行1.28ms
2025-07-31 18:23:17.482 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=13, 渲染时间=16.7ms, 策略=small_dataset
2025-07-31 18:23:17.482 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-07-31 18:23:17.482 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2598 | [数据流追踪] 优化渲染完成: 13行 x 28列, 策略=small_dataset, 耗时=16.7ms, 性能评级=excellent
2025-07-31 18:23:17.482 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 未知0
2025-07-31 18:23:17.482 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 未知1
2025-07-31 18:23:17.482 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 未知2
2025-07-31 18:23:17.482 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 未知3
2025-07-31 18:23:17.482 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 未知4
2025-07-31 18:23:17.482 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 13
2025-07-31 18:23:17.482 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:474 | 🚨 [UI数据修复] 无法找到工号字段，first_row存在: True
2025-07-31 18:23:17.482 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:476 | 🚨 [UI数据修复] main_data字段: ['人员代码', '姓名', '部门名称', '人员类别代码', '基本退休费', '津贴', '结余津贴', '离退休生活补贴', '护理费', '物业补贴', '住房补贴', '增资预付', '2016待遇调整', '2017待遇调整', '2018待遇调整', '2019待遇调整', '2020待遇调整', '2021待遇调整', '2022待遇调整', '2023待遇调整', '补发', '借支', '应发工资', '公积', '保险扣款', '备注', '月份', '年份']
2025-07-31 18:23:17.482 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 13 行, 28 列
2025-07-31 18:23:17.496 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:409 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_07_pension_employees
2025-07-31 18:23:17.496 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:440 | 🔧 [新架构] 为表格 salary_data_2025_07_pension_employees 重新加载 32 个字段映射
2025-07-31 18:23:17.496 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2678 | 表格数据设置完成: 13 行, 耗时: 77.9ms
2025-07-31 18:23:17.496 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7115 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-31 18:23:17.496 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7128 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-31 18:23:17.496 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1632 | 🆕 [列宽保存] 未找到表格 salary_data_2025_07_pension_employees 的列宽配置
2025-07-31 18:23:17.496 | INFO     | src.gui.widgets.pagination_widget:set_total_records:490 | 总记录数设置为: 13
2025-07-31 18:23:17.503 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3400 | 🔧 [统一数据设置] 数据已成功设置到UI: 13行, 28列
2025-07-31 18:23:17.514 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3433 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=1, 总记录数=13
2025-07-31 18:23:17.515 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3439 | 🔧 [分页修复] 按钮可用性: 下一页=False, 上一页=False
2025-07-31 18:23:17.515 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3450 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-13
2025-07-31 18:23:17.516 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3721 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-07-31 18:23:17.517 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3722 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 13, 'start_record': 1, 'end_record': 13}
2025-07-31 18:23:17.518 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3723 | 🔍 [表格调试] 当前表格行数: 13
2025-07-31 18:23:17.518 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3724 | 🔍 [表格调试] 当前表格列数: 28
2025-07-31 18:23:17.519 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3726 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-13
2025-07-31 18:23:17.519 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3752 | 🔧 [分页行号修复] 表格状态: 当前行数=13, 期望行数=13
2025-07-31 18:23:17.520 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 28 个, 行号起始 1, 共 13 行
2025-07-31 18:23:17.521 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:3831 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共13行
2025-07-31 18:23:17.521 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3452 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-07-31 18:23:17.576 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-07-31 18:23:17.577 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:8793 | ✅ [新架构] 组件状态一致性验证通过
2025-07-31 18:23:17.580 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:8756 | 🆕 [新架构] 导航迁移完成
2025-07-31 18:23:17.580 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3466 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-07-31 18:23:17.581 | INFO     | src.services.table_data_service:load_table_data:510 | [修复数据发布] 数据更新事件已发布
2025-07-31 18:23:17.582 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:6120 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-07-31 18:23:17.582 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:882 | 导航选择: 工资表 > 2025年 > 7月 > 退休人员
2025-07-31 18:23:25.894 | INFO     | __main__:main:445 | 应用程序正常退出
2025-07-31 18:23:25.899 | INFO     | src.gui.table_header_manager:cleanup_callback:131 | 🔧 [P1-1] 表格 table_0_2604849907904 已自动清理（弱引用回调）
