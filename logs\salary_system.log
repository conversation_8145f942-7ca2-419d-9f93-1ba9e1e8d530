2025-07-31 18:05:57.146 | INFO     | src.utils.log_config:_log_initialization_info:205 | 日志系统初始化完成
2025-07-31 18:05:57.146 | INFO     | src.utils.log_config:_log_initialization_info:206 | 日志级别: INFO
2025-07-31 18:05:57.146 | INFO     | src.utils.log_config:_log_initialization_info:207 | 控制台输出: True
2025-07-31 18:05:57.146 | INFO     | src.utils.log_config:_log_initialization_info:208 | 文件输出: True
2025-07-31 18:05:57.146 | INFO     | src.utils.log_config:_log_initialization_info:214 | 日志文件路径: logs/salary_system.log
2025-07-31 18:05:57.146 | INFO     | src:<module>:20 | 月度工资异动处理系统 v1.0.0 初始化完成
2025-07-31 18:05:59.662 | INFO     | src.gui.style_manager:__init__:68 | StyleManager初始化完成
2025-07-31 18:05:59.662 | INFO     | src.gui.style_manager:apply_global_style:93 | 开始应用全局Material Design样式...
2025-07-31 18:05:59.662 | INFO     | src.gui.style_manager:apply_global_style:105 | 全局样式应用成功
2025-07-31 18:05:59.662 | INFO     | src.gui.style_manager:enable_hot_reload:416 | 样式热重载未启用（非开发环境）
2025-07-31 18:05:59.662 | INFO     | __main__:setup_qt_exception_handling:218 | 🔧 [P0-修复] PyQt专用异常处理机制已启用
2025-07-31 18:05:59.662 | INFO     | __main__:setup_app_logging:349 | 月度工资异动处理系统 v2.0.0-refactored 启动
2025-07-31 18:05:59.662 | INFO     | __main__:main:413 | 初始化核心管理器...
2025-07-31 18:05:59.662 | INFO     | src.modules.system_config.config_manager:__init__:313 | 配置管理器初始化完成，配置文件: config.json
2025-07-31 18:05:59.662 | INFO     | src.modules.system_config.config_manager:load_config:340 | 正在加载配置文件: config.json
2025-07-31 18:05:59.662 | INFO     | src.modules.system_config.config_manager:_generate_validation_result:252 | 配置文件验证通过
2025-07-31 18:05:59.662 | INFO     | src.modules.system_config.config_manager:load_config:352 | 配置文件加载成功
2025-07-31 18:05:59.662 | INFO     | src.modules.data_storage.database_manager:_initialize_database:101 | 开始初始化数据库...
2025-07-31 18:05:59.725 | INFO     | src.modules.data_storage.database_manager:_initialize_database:156 | 数据库初始化完成
2025-07-31 18:05:59.725 | INFO     | src.modules.data_storage.database_manager:__init__:97 | 数据库管理器初始化完成，数据库路径: C:\test\salary_changes\salary_changes\data\db\salary_system.db
2025-07-31 18:05:59.725 | INFO     | src.modules.system_config.config_manager:__init__:313 | 配置管理器初始化完成，配置文件: config.json
2025-07-31 18:05:59.725 | INFO     | src.modules.data_storage.dynamic_table_manager:__init__:104 | 动态表管理器初始化完成
2025-07-31 18:05:59.725 | INFO     | __main__:main:418 | 核心管理器初始化完成。
2025-07-31 18:05:59.725 | INFO     | src.core.data_source_unification_manager:__init__:128 | 数据源统一管理器初始化完成
2025-07-31 18:05:59.725 | INFO     | src.core.table_sort_state_manager:__init__:174 | 表级排序状态管理器初始化完成
2025-07-31 18:05:59.725 | INFO     | src.core.event_bus:__init__:227 | 事件总线初始化完成
2025-07-31 18:05:59.725 | INFO     | src.core.error_handler_manager:__init__:111 | 🔧 [P2-3] 错误处理管理器初始化完成
2025-07-31 18:05:59.725 | INFO     | src.core.error_handler_manager:register_recovery_strategy:368 | 🔧 [P2-3] 注册恢复策略: data_error_medium
2025-07-31 18:05:59.725 | INFO     | src.core.error_handler_manager:register_recovery_strategy:368 | 🔧 [P2-3] 注册恢复策略: ui_error_medium
2025-07-31 18:05:59.725 | INFO     | src.core.error_handler_manager:register_recovery_strategy:368 | 🔧 [P2-3] 注册恢复策略: system_error_medium
2025-07-31 18:05:59.740 | INFO     | src.gui.prototype.prototype_main_window:_register_recovery_strategies:9574 | 🔧 [P2-3] 错误恢复策略注册完成
2025-07-31 18:05:59.740 | INFO     | src.core.event_bus:__init__:227 | 事件总线初始化完成
2025-07-31 18:05:59.740 | INFO     | src.gui.prototype.prototype_main_window:_setup_error_handling:9429 | 🔧 [P2-3] 错误处理机制设置完成
2025-07-31 18:05:59.740 | INFO     | src.gui.prototype.prototype_main_window:_init_recursive_call_protection:9467 | 🔧 [P1-2] 递归调用防护机制初始化完成
2025-07-31 18:05:59.803 | INFO     | src.core.architecture_factory:__init__:64 | 架构重构工厂初始化完成
2025-07-31 18:05:59.818 | INFO     | src.core.architecture_factory:initialize_architecture:74 | 开始初始化架构重构系统...
2025-07-31 18:05:59.818 | INFO     | src.core.event_bus:__init__:227 | 事件总线初始化完成
2025-07-31 18:05:59.930 | WARNING  | src.modules.data_import.config_sync_manager:_do_config_initialization:139 | 创建了最小默认配置，可能需要重新导入数据以生成字段映射
2025-07-31 18:05:59.930 | INFO     | src.modules.data_import.config_sync_manager:__init__:81 | 🆕 [新架构] 配置同步管理器初始化完成（依赖注入）
2025-07-31 18:05:59.930 | INFO     | src.core.unified_state_manager:_load_state:465 | 状态文件不存在，使用默认状态
2025-07-31 18:05:59.930 | INFO     | src.core.unified_state_manager:__init__:177 | 统一状态管理器初始化完成
2025-07-31 18:05:59.930 | INFO     | src.core.unified_data_request_manager:__init__:190 | 统一数据请求管理器初始化完成
2025-07-31 18:05:59.930 | INFO     | src.core.unified_data_request_manager:__init__:190 | 统一数据请求管理器初始化完成
2025-07-31 18:05:59.930 | INFO     | src.core.unified_state_manager:_load_state:465 | 状态文件不存在，使用默认状态
2025-07-31 18:05:59.930 | INFO     | src.core.unified_state_manager:__init__:177 | 统一状态管理器初始化完成
2025-07-31 18:05:59.930 | INFO     | src.services.table_data_service:__init__:81 | 表格数据服务初始化完成
2025-07-31 18:05:59.930 | INFO     | src.core.architecture_factory:initialize_architecture:105 | 🎉 架构重构系统初始化成功！耗时: 111.9ms
2025-07-31 18:05:59.962 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:46 | 数据预加载缓存初始化完成 - 最大条目数: 100, TTL: 300秒
2025-07-31 18:05:59.962 | INFO     | src.gui.prototype.performance.data_preload_cache:__init__:232 | 表格状态缓存初始化完成
2025-07-31 18:05:59.962 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:32 | 表头配置缓存初始化完成
2025-07-31 18:05:59.962 | INFO     | src.gui.prototype.performance.header_config_cache:__init__:211 | 字段映射缓存初始化完成
2025-07-31 18:05:59.962 | INFO     | src.gui.prototype.performance.performance_manager:__init__:46 | 🚀 性能管理器初始化完成
2025-07-31 18:05:59.962 | INFO     | src.gui.prototype.prototype_main_window:__init__:3147 | 🚀 性能管理器已集成
2025-07-31 18:05:59.962 | INFO     | src.gui.prototype.prototype_main_window:__init__:3149 | ✅ 新架构集成成功！
2025-07-31 18:05:59.962 | INFO     | src.gui.prototype.prototype_main_window:_inject_config_sync_manager_to_existing_tables:3261 | ConfigSyncManager重新注入完成，已更新0个表格实例
2025-07-31 18:05:59.962 | INFO     | src.gui.prototype.prototype_main_window:_setup_new_architecture_listeners:3227 | ✅ 新架构事件监听器设置完成
2025-07-31 18:05:59.962 | INFO     | src.gui.widgets.pagination_cache_manager:__init__:107 | 分页缓存管理器初始化完成 - 最大条目数: 50, 最大内存: 50MB
2025-07-31 18:05:59.962 | INFO     | src.gui.prototype.managers.responsive_layout_manager:__init__:146 | 响应式布局管理器初始化完成
2025-07-31 18:05:59.962 | INFO     | src.gui.prototype.managers.communication_manager:__init__:134 | 组件通信管理器初始化完成
2025-07-31 18:06:00.196 | INFO     | src.gui.prototype.prototype_main_window:_create_menu_bar:2360 | 菜单栏创建完成
2025-07-31 18:06:00.196 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-07-31 18:06:00.212 | INFO     | src.modules.system_config.user_preferences:load_preferences:183 | 正在加载偏好设置: user_preferences.json
2025-07-31 18:06:00.212 | INFO     | src.modules.system_config.user_preferences:load_preferences:193 | 偏好设置加载成功
2025-07-31 18:06:00.212 | INFO     | src.gui.prototype.prototype_main_window:__init__:2336 | 菜单栏管理器初始化完成
2025-07-31 18:06:00.212 | INFO     | src.gui.table_header_manager:__init__:104 | 🔧 [P1-3] 性能优化表头管理器初始化完成
2025-07-31 18:06:00.212 | INFO     | src.gui.prototype.prototype_main_window:_setup_managers:4552 | 管理器设置完成，包含增强版表头管理器
2025-07-31 18:06:00.212 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:4557 | 🔧 开始应用窗口级Material Design样式...
2025-07-31 18:06:00.212 | INFO     | src.gui.style_manager:apply_window_style:123 | 开始应用窗口级样式...
2025-07-31 18:06:00.212 | INFO     | src.gui.style_manager:apply_window_style:136 | 窗口级样式应用成功
2025-07-31 18:06:00.212 | INFO     | src.gui.prototype.prototype_main_window:_apply_window_style:4564 | ✅ 窗口级样式应用成功
2025-07-31 18:06:00.212 | INFO     | src.gui.prototype.prototype_main_window:_setup_responsive_style:4605 | ✅ 响应式样式监听设置完成
2025-07-31 18:06:00.212 | INFO     | src.gui.prototype.widgets.smart_search_debounce:__init__:368 | 智能防抖搜索算法初始化完成
2025-07-31 18:06:00.243 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:__init__:148 | 智能树形展开算法初始化完成
2025-07-31 18:06:00.243 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1250 | 开始从元数据动态加载工资数据...
2025-07-31 18:06:00.243 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_load_dynamic_salary_data:1257 | 检测到首次启动，将延迟加载导航数据确保数据库就绪
2025-07-31 18:06:00.256 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_setup_navigation_data:765 | 导航面板已重构：移除功能性导航，专注数据导航
2025-07-31 18:06:00.260 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_restore_state:800 | 恢复导航状态: 0个展开项
2025-07-31 18:06:00.261 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表', '工资表 > 2025年']
2025-07-31 18:06:00.264 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:__init__:559 | 增强导航面板初始化完成
2025-07-31 18:06:00.531 | INFO     | src.gui.prototype.widgets.header_update_manager:__init__:68 | HeaderUpdateManager 初始化完成，关联表格: VirtualizedExpandableTable
2025-07-31 18:06:00.532 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2046 | 🚨 [架构修复] 启用模型数据同步机制
2025-07-31 18:06:00.540 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_register_shortcuts:1326 | 快捷键注册完成: 18/18 个
2025-07-31 18:06:00.540 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1714 | 拖拽排序管理器初始化完成
2025-07-31 18:06:00.541 | INFO     | src.modules.data_import.header_edit_manager:__init__:82 | 表头编辑管理器初始化完成
2025-07-31 18:06:00.542 | INFO     | src.core.unified_state_manager:_load_state:465 | 状态文件不存在，使用默认状态
2025-07-31 18:06:00.542 | INFO     | src.core.unified_state_manager:__init__:177 | 统一状态管理器初始化完成
2025-07-31 18:06:00.543 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2121 | 🧹 [代码清理] 格式管理器已迁移到MasterFormatManager
2025-07-31 18:06:00.554 | INFO     | src.gui.prototype.widgets.column_sort_manager:_load_field_mappings_from_config:337 | 🔧 [新架构] 成功加载 46 个字段映射
2025-07-31 18:06:00.555 | INFO     | src.gui.prototype.widgets.column_sort_manager:__init__:99 | 🆕 [新架构排序] 多列排序管理器初始化完成，最大排序列数: 3
2025-07-31 18:06:00.556 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2163 | 🆕 [新架构多列排序] 初始化完成，支持最多3列排序
2025-07-31 18:06:00.557 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1510 | 🔧 [列宽保存修复] 列宽管理器初始化完成
2025-07-31 18:06:00.561 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1511 | 🔧 [列宽保存修复] 配置文件绝对路径: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-07-31 18:06:00.562 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1512 | 🔧 [列宽保存修复] 配置文件存在: False
2025-07-31 18:06:00.563 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1513 | 🔧 [列宽保存修复] 父目录存在: True
2025-07-31 18:06:00.565 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:1514 | 🔧 [列宽保存修复] 当前工作目录: C:\test\salary_changes\salary_changes
2025-07-31 18:06:00.568 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2170 | 列宽管理器初始化完成
2025-07-31 18:06:00.569 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:setup_ui:2201 | 🔧 [架构修复] 保持自定义排序功能，修复数据显示问题
2025-07-31 18:06:00.574 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:__init__:2184 | 虚拟化可展开表格初始化完成 - Phase 4核心交互功能增强版，最大可见行数: 1000
2025-07-31 18:06:00.577 | WARNING  | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:1998 | 无法获取主窗口引用，使用备用方案显示空表格
2025-07-31 18:06:00.578 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4587 | 数据量为0，不调整最大可见行数，保持当前值: 1000
2025-07-31 18:06:00.578 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2532 | 表格格式化完成: default_table, 类型: active_employees
2025-07-31 18:06:00.599 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 0行数据
2025-07-31 18:06:00.600 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:242 | [数据流追踪] 检测到空数据，使用快速清空策略
2025-07-31 18:06:00.603 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2598 | [数据流追踪] 优化渲染完成: 0行 x 22列, 策略=empty_data, 耗时=0.0ms, 性能评级=excellent
2025-07-31 18:06:00.605 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 0
2025-07-31 18:06:00.605 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 0 行, 22 列
2025-07-31 18:06:00.612 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2678 | 表格数据设置完成: 0 行, 耗时: 35.1ms
2025-07-31 18:06:00.615 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7115 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-31 18:06:00.617 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7128 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-31 18:06:00.618 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1618 | 🔧 [列宽保存修复] 配置文件不存在: C:\test\salary_changes\salary_changes\state\column_widths.json，使用默认列宽
2025-07-31 18:06:00.619 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_fallback:2020 | 🔧 [P0-修复] 使用备用方案显示了标准空表格，表头数量: 22个
2025-07-31 18:06:00.638 | INFO     | src.gui.widgets.pagination_widget:_apply_styles:344 | 分页组件Material Design样式应用成功
2025-07-31 18:06:00.647 | INFO     | src.gui.widgets.pagination_widget:__init__:174 | ✅ [防抖升级] 智能防抖系统已启用
2025-07-31 18:06:00.648 | INFO     | src.gui.widgets.pagination_widget:__init__:182 | 分页组件初始化完成
2025-07-31 18:06:00.737 | INFO     | src.gui.prototype.prototype_main_window:_connect_control_panel_signals:523 | 控制面板按钮信号连接完成
2025-07-31 18:06:00.794 | INFO     | src.modules.system_config.config_manager:__init__:313 | 配置管理器初始化完成，配置文件: config.json
2025-07-31 18:06:00.796 | INFO     | src.modules.system_config.user_preferences:__init__:61 | 用户偏好设置管理器初始化完成，偏好文件: C:\test\salary_changes\salary_changes\user_preferences.json
2025-07-31 18:06:00.797 | INFO     | src.gui.prototype.prototype_main_window:_setup_shortcuts:4522 | 快捷键设置完成
2025-07-31 18:06:00.797 | INFO     | src.gui.prototype.prototype_main_window:_setup_ui:4511 | 主窗口UI设置完成。
2025-07-31 18:06:00.798 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:4708 | 🔧 [全局排序] 全局排序开关连接成功
2025-07-31 18:06:00.799 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:4740 | 🆕 [新架构排序] 使用表格组件内部的自定义排序循环，无需连接排序信号
2025-07-31 18:06:00.800 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:4749 | ✅ 已连接分页组件事件到新架构
2025-07-31 18:06:00.801 | INFO     | src.gui.prototype.prototype_main_window:_setup_connections:4751 | 信号连接设置完成
2025-07-31 18:06:00.802 | INFO     | src.gui.prototype.prototype_main_window:_load_field_mappings_from_file:5679 | 已加载字段映射信息，共0个表的映射
2025-07-31 18:06:00.810 | WARNING  | src.gui.prototype.prototype_main_window:set_data:688 | 尝试设置空数据，将显示提示信息。
2025-07-31 18:06:00.814 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6895 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-07-31 18:06:00.816 | WARNING  | src.gui.prototype.prototype_main_window:set_data:672 | 🔧 [P0-2] 检测到递归设置数据，跳过以防止表头重影
2025-07-31 18:06:00.818 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6910 | 已显示标准空表格，表头数量: 22
2025-07-31 18:06:00.819 | INFO     | src.gui.widgets.pagination_widget:reset:620 | 分页状态已重置
2025-07-31 18:06:00.820 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6895 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-07-31 18:06:00.822 | WARNING  | src.gui.prototype.prototype_main_window:set_data:708 | 尝试设置空数据，将显示提示信息。
2025-07-31 18:06:00.828 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6895 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-07-31 18:06:00.829 | WARNING  | src.gui.prototype.prototype_main_window:set_data:672 | 🔧 [P0-2] 检测到递归设置数据，跳过以防止表头重影
2025-07-31 18:06:00.830 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6910 | 已显示标准空表格，表头数量: 22
2025-07-31 18:06:00.831 | INFO     | src.gui.widgets.pagination_widget:reset:620 | 分页状态已重置
2025-07-31 18:06:00.831 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6910 | 已显示标准空表格，表头数量: 22
2025-07-31 18:06:00.832 | INFO     | src.gui.prototype.prototype_main_window:__init__:3201 | 原型主窗口初始化完成
2025-07-31 18:06:01.054 | INFO     | __main__:main:440 | 应用程序启动成功
2025-07-31 18:06:01.060 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:7512 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-07-31 18:06:01.063 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:7526 | 🔧 [P0-1] 检测到亮度问题: ['table_opacity_in_stylesheet', 'parent_opacity_effect', 'table_dark_background']
2025-07-31 18:06:01.065 | INFO     | src.gui.prototype.prototype_main_window:_apply_brightness_fixes:7941 | 🔧 [P1-2] 开始应用增强版亮度修复: ['table_opacity_in_stylesheet', 'parent_opacity_effect', 'table_dark_background']
2025-07-31 18:06:01.183 | INFO     | src.gui.prototype.managers.responsive_layout_manager:_apply_layout:206 | 断点切换: sm (宽度: 1280px)
2025-07-31 18:06:01.183 | INFO     | src.gui.prototype.prototype_main_window:handle_responsive_change:1838 | MainWorkspaceArea 响应式适配: sm
2025-07-31 18:06:01.189 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_load_salary_data:1272 | 执行延迟的工资数据加载...
2025-07-31 18:06:01.190 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1553 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-07-31 18:06:01.191 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1561 | 找到工资表节点: 💰 工资表
2025-07-31 18:06:01.197 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-31 18:06:01.199 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_navigation_tree_data:976 | 在 table_metadata 中未找到任何 'salary_data' 类型的表
2025-07-31 18:06:01.200 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:888 | 找到 3 个总表
2025-07-31 18:06:01.200 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1613 | 检测到数据库中没有工资数据表，直接使用兜底数据
2025-07-31 18:06:01.201 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_load_fallback_salary_data:1395 | 使用兜底数据加载导航
2025-07-31 18:06:01.226 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1211 | 执行延迟的自动选择最新数据...
2025-07-31 18:06:01.227 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1151 | 开始自动选择最新数据...
2025-07-31 18:06:01.230 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1036 | 开始获取最新工资数据路径...
2025-07-31 18:06:01.235 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-31 18:06:01.261 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1041 | 未找到任何工资数据表
2025-07-31 18:06:01.285 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:auto_select_latest_data:1156 | 未找到最新工资数据路径，无法自动选择
2025-07-31 18:06:01.286 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_delayed_auto_select_latest_data:1231 | 延迟自动选择最新数据失败，可能没有可用数据
2025-07-31 18:06:01.287 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:7512 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-07-31 18:06:01.306 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:7532 | 🔧 [P0-1] 智能显示亮度修复完成
2025-07-31 18:06:01.348 | WARNING  | src.gui.prototype.prototype_main_window:_check_method_call_protection:9513 | 🔧 [P1-2] 方法 _fix_display_brightness_after_data_refresh 每秒调用次数超限: 2
2025-07-31 18:06:01.348 | INFO     | src.gui.prototype.prototype_main_window:_apply_cooldown:9543 | 🔧 [P1-2] 对方法 _fix_display_brightness_after_data_refresh 应用 5.0秒 冷却期
2025-07-31 18:06:01.388 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1597 | 🔧 [列宽保存修复] 准备保存列宽到文件: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-07-31 18:06:01.389 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1598 | 🔧 [列宽保存修复] 表名: default_table, 列数: 22
2025-07-31 18:06:01.403 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1603 | 🔧 [列宽保存修复] 列宽设置保存成功！文件大小: 904 字节
2025-07-31 18:06:01.406 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1301 | 导航树刷新完成，重新执行自动选择...
2025-07-31 18:06:01.406 | INFO     | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1036 | 开始获取最新工资数据路径...
2025-07-31 18:06:01.406 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-31 18:06:01.406 | WARNING  | src.modules.data_storage.dynamic_table_manager:get_latest_salary_data_path:1041 | 未找到任何工资数据表
2025-07-31 18:06:01.406 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_post_refresh_auto_select:1306 | 未找到最新工资数据路径
2025-07-31 18:06:08.278 | INFO     | src.gui.style_manager:update_responsive_style:227 | 响应式样式更新: medium -> large
2025-07-31 18:06:08.278 | INFO     | src.gui.style_manager:update_responsive_style:238 | 响应式样式更新成功: large
2025-07-31 18:06:08.379 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:7512 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-07-31 18:06:08.463 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:7512 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-07-31 18:06:28.047 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data:583 | 数据导入功能被触发，发出 import_requested 信号。
2025-07-31 18:06:28.047 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:4982 | 接收到数据导入请求，推断的目标路径: 工资表 > 2025年 > 07月 > 全部在职人员。打开导入对话框。
2025-07-31 18:06:28.063 | INFO     | src.modules.data_import.auto_field_mapping_generator:__init__:60 | 自动字段映射生成器初始化完成
2025-07-31 18:06:28.063 | INFO     | src.modules.data_import.multi_sheet_importer:__init__:72 | 多Sheet导入器初始化完成
2025-07-31 18:06:28.063 | INFO     | src.gui.widgets.target_selection_widget:_save_config:285 | 配置保存成功
2025-07-31 18:06:28.080 | INFO     | src.gui.widgets.target_selection_widget:set_target_from_path:505 | 从路径设置目标: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-31 18:06:28.080 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 0 个匹配类型 'salary_data' 的表
2025-07-31 18:06:28.112 | INFO     | src.gui.main_dialogs:_get_template_fields:1872 | 🔧 [P3-修复] ConfigSyncManager未初始化，使用默认字段模板
2025-07-31 18:06:28.126 | INFO     | src.gui.main_dialogs:_init_field_mapping:1859 | 🔧 [P3-修复] 未找到字段模板，使用内置默认字段列表
2025-07-31 18:06:28.172 | INFO     | src.modules.data_import.import_defaults_manager:load_settings:86 | 未找到用户设置文件，使用默认设置
2025-07-31 18:06:28.172 | INFO     | src.gui.main_dialogs:_apply_default_settings:2210 | 已应用默认设置: {'start_row': 1, 'import_mode': 'multi_sheet', 'auto_match_sheet': True, 'include_header': True, 'skip_empty_rows': True, 'create_table_mode': 'sheet_name', 'import_strategy': 'separate_tables', 'table_template': 'salary_data'}
2025-07-31 18:06:28.172 | INFO     | src.gui.main_dialogs:_setup_tooltips:2465 | 工具提示设置完成
2025-07-31 18:06:28.172 | INFO     | src.gui.main_dialogs:_setup_shortcuts:2504 | 快捷键设置完成
2025-07-31 18:06:28.172 | INFO     | src.gui.main_dialogs:__init__:77 | 数据导入对话框初始化完成。
2025-07-31 18:06:28.172 | INFO     | src.modules.data_import.multi_sheet_importer:set_config_sync_manager:81 | 🔧 [修复] ConfigSyncManager已设置到MultiSheetImporter
2025-07-31 18:06:28.172 | INFO     | src.gui.prototype.prototype_main_window:_on_import_data_requested:4993 | 🔧 [P0-修复] ConfigSyncManager已设置到数据导入对话框
2025-07-31 18:06:28.257 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:7512 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-07-31 18:06:36.007 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-31 18:06:37.958 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-31 18:06:37.961 | INFO     | src.modules.data_import.smart_sheet_matcher:record_user_choice:372 | 记录用户选择: 全部在职人员 -> 全部在职人员工资表
2025-07-31 18:06:37.962 | INFO     | src.gui.main_dialogs:_auto_select_sheet_by_category:2245 | 根据人员类别 '全部在职人员' 自动选择工作表: 全部在职人员工资表 (匹配类型: fuzzy, 得分: 0.60)
2025-07-31 18:06:39.447 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-31 18:06:39.634 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-31 18:06:39.650 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-31 18:06:39.650 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:211 | 开始导入多Sheet Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-31 18:06:39.650 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-31 18:06:39.853 | INFO     | src.modules.data_import.excel_importer:get_sheet_names:173 | 检测到工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-31 18:06:39.853 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:222 | 检测到 4 个工作表: ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']
2025-07-31 18:06:39.853 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-31 18:06:39.853 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-31 18:06:39.853 | INFO     | src.utils.log_config:log_file_operation:252 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-31 18:06:39.962 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 16列 (列过滤: 否)
2025-07-31 18:06:39.962 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-31 18:06:39.962 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 16 列 (原始 16 列)
2025-07-31 18:06:39.962 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 3行 x 16列
2025-07-31 18:06:39.962 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-07-31 18:06:39.962 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始3条记录，过滤1条无效记录，有效记录2条
2025-07-31 18:06:39.962 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 2行 × 16列
2025-07-31 18:06:39.962 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 离休人员工资表 使用智能默认配置
2025-07-31 18:06:39.978 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet '离休人员工资表' 生成智能默认配置: 1 个必需字段
2025-07-31 18:06:39.978 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 retired_employees 生成了 21 个字段映射
2025-07-31 18:06:39.978 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 retired_employees 生成字段映射: 21 个字段
2025-07-31 18:06:39.978 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:615 | 完整字段映射保存成功: salary_data_2025_07_retired_employees
2025-07-31 18:06:39.978 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_07_retired_employees 生成标准化字段映射: 21 个字段
2025-07-31 18:06:39.994 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet 离休人员工资表 数据处理完成: 2 行
2025-07-31 18:06:39.994 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet '离休人员工资表' 检测到模板类型: retired_employees
2025-07-31 18:06:40.009 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_07_retired_employees
2025-07-31 18:06:40.009 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_retired_employees (模板: retired_employees)
2025-07-31 18:06:40.041 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | 🔧 [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-07-31 18:06:40.059 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-31 18:06:40.059 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | 🔧 [修复标识] 导入列名映射成功: 18 个字段已映射
2025-07-31 18:06:40.059 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。
2025-07-31 18:06:40.071 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-31 18:06:40.071 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-31 18:06:40.071 | INFO     | src.utils.log_config:log_file_operation:252 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-31 18:06:40.165 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 27列 (列过滤: 否)
2025-07-31 18:06:40.165 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-31 18:06:40.165 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 27 列 (原始 27 列)
2025-07-31 18:06:40.165 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 14行 x 27列
2025-07-31 18:06:40.165 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-07-31 18:06:40.165 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始14条记录，过滤1条无效记录，有效记录13条
2025-07-31 18:06:40.181 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 13行 × 27列
2025-07-31 18:06:40.181 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 退休人员工资表 使用智能默认配置
2025-07-31 18:06:40.181 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet '退休人员工资表' 生成智能默认配置: 1 个必需字段
2025-07-31 18:06:40.181 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 pension_employees 生成了 32 个字段映射
2025-07-31 18:06:40.181 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 pension_employees 生成字段映射: 32 个字段
2025-07-31 18:06:40.181 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:615 | 完整字段映射保存成功: salary_data_2025_07_pension_employees
2025-07-31 18:06:40.181 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_07_pension_employees 生成标准化字段映射: 32 个字段
2025-07-31 18:06:40.197 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet 退休人员工资表 数据处理完成: 13 行
2025-07-31 18:06:40.197 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet '退休人员工资表' 检测到模板类型: pension_employees
2025-07-31 18:06:40.197 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_07_pension_employees
2025-07-31 18:06:40.197 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_pension_employees (模板: pension_employees)
2025-07-31 18:06:40.213 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | 🔧 [修复标识] 导入字段映射加载完成: 32 个映射规则
2025-07-31 18:06:40.213 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-31 18:06:40.213 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | 🔧 [修复标识] 导入列名映射成功: 29 个字段已映射
2025-07-31 18:06:40.229 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。
2025-07-31 18:06:40.229 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-31 18:06:40.229 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-31 18:06:40.229 | INFO     | src.utils.log_config:log_file_operation:252 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-31 18:06:40.353 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 23列 (列过滤: 否)
2025-07-31 18:06:40.353 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-31 18:06:40.353 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 23 列 (原始 23 列)
2025-07-31 18:06:40.369 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 1397行 x 23列
2025-07-31 18:06:40.369 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-07-31 18:06:40.369 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始1397条记录，过滤1条无效记录，有效记录1396条
2025-07-31 18:06:40.369 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 1396行 × 23列
2025-07-31 18:06:40.369 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 全部在职人员工资表 使用智能默认配置
2025-07-31 18:06:40.369 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet '全部在职人员工资表' 生成智能默认配置: 2 个必需字段
2025-07-31 18:06:40.369 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 active_employees 生成了 28 个字段映射
2025-07-31 18:06:40.369 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 active_employees 生成字段映射: 28 个字段
2025-07-31 18:06:40.384 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:615 | 完整字段映射保存成功: salary_data_2025_07_active_employees
2025-07-31 18:06:40.384 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_07_active_employees 生成标准化字段映射: 28 个字段
2025-07-31 18:06:40.384 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet 全部在职人员工资表 数据处理完成: 1396 行
2025-07-31 18:06:40.384 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet '全部在职人员工资表' 检测到模板类型: active_employees
2025-07-31 18:06:40.400 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_07_active_employees
2025-07-31 18:06:40.400 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_active_employees (模板: active_employees)
2025-07-31 18:06:40.416 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | 🔧 [修复标识] 导入字段映射加载完成: 28 个映射规则
2025-07-31 18:06:40.416 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-31 18:06:40.416 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | 🔧 [修复标识] 导入列名映射成功: 25 个字段已映射
2025-07-31 18:06:40.462 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。
2025-07-31 18:06:40.462 | INFO     | src.modules.data_import.excel_importer:validate_file:111 | 文件验证成功: 2025年5月份正式工工资（报财务)  最终版.xls (1.17MB)
2025-07-31 18:06:40.462 | INFO     | src.modules.data_import.excel_importer:import_data:260 | 开始导入Excel文件: 2025年5月份正式工工资（报财务)  最终版.xls
2025-07-31 18:06:40.462 | INFO     | src.utils.log_config:log_file_operation:252 | 文件C:\test\salary_changes\salary_changes\data\工资表\2025年5月份正式工工资（报财务)  最终版.xls: read
2025-07-31 18:06:40.572 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:320 | 🔧 [修复标识] Excel读取完成: 21列 (列过滤: 否)
2025-07-31 18:06:40.572 | INFO     | src.modules.data_import.excel_importer:_clean_data:427 | 🔧 [修复标识] Excel导入器 _clean_data 方法 - 保留所有列结构修复已执行
2025-07-31 18:06:40.572 | INFO     | src.modules.data_import.excel_importer:_clean_data:438 | 数据清理完成: 保留所有 21 列 (原始 21 列)
2025-07-31 18:06:40.572 | INFO     | src.modules.data_import.excel_importer:_import_normal_file:339 | 导入完成: 63行 x 21列
2025-07-31 18:06:40.572 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:567 | 数据质量检查: 已过滤1条姓名为空的记录
2025-07-31 18:06:40.572 | INFO     | src.modules.data_import.excel_importer:_filter_invalid_data:608 | 数据过滤完成: 原始63条记录，过滤1条无效记录，有效记录62条
2025-07-31 18:06:40.572 | INFO     | src.modules.data_import.excel_importer:import_data:286 | 🔧 [修复标识] 数据导入最终完成: 62行 × 21列
2025-07-31 18:06:40.572 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:456 | 工作表 A岗职工 使用智能默认配置
2025-07-31 18:06:40.572 | INFO     | src.modules.data_import.multi_sheet_importer:_generate_smart_default_config:744 | 为Sheet 'A岗职工' 生成智能默认配置: 2 个必需字段
2025-07-31 18:06:40.572 | INFO     | src.modules.data_import.specialized_field_mapping_generator:generate_mapping:139 | 为模板 a_grade_employees 生成了 26 个字段映射
2025-07-31 18:06:40.572 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:473 | 使用专用模板 a_grade_employees 生成字段映射: 26 个字段
2025-07-31 18:06:40.588 | INFO     | src.modules.data_import.config_sync_manager:save_complete_mapping:615 | 完整字段映射保存成功: salary_data_2025_07_a_grade_employees
2025-07-31 18:06:40.588 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:520 | 为表 salary_data_2025_07_a_grade_employees 生成标准化字段映射: 26 个字段
2025-07-31 18:06:40.588 | INFO     | src.modules.data_import.multi_sheet_importer:_process_sheet_data:541 | Sheet A岗职工 数据处理完成: 62 行
2025-07-31 18:06:40.588 | INFO     | src.modules.data_import.multi_sheet_importer:_import_separate_strategy:371 | Sheet 'A岗职工' 检测到模板类型: a_grade_employees
2025-07-31 18:06:40.603 | INFO     | src.modules.data_storage.dynamic_table_manager:_create_table_from_schema:700 | 成功创建表: salary_data_2025_07_a_grade_employees
2025-07-31 18:06:40.603 | INFO     | src.modules.data_storage.dynamic_table_manager:create_specialized_salary_table:360 | 专用工资数据表创建成功: salary_data_2025_07_a_grade_employees (模板: a_grade_employees)
2025-07-31 18:06:40.619 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1135 | 🔧 [修复标识] 导入字段映射加载完成: 26 个映射规则
2025-07-31 18:06:40.619 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1143 | 🔧 [修复标识] 保留未映射列: ['data_source', 'import_time']
2025-07-31 18:06:40.619 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1152 | 🔧 [修复标识] 导入列名映射成功: 23 个字段已映射
2025-07-31 18:06:40.634 | INFO     | src.modules.data_storage.dynamic_table_manager:save_dataframe_to_table:1289 | 成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。
2025-07-31 18:06:40.634 | INFO     | src.modules.data_import.multi_sheet_importer:import_excel_file:241 | 多Sheet导入完成: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_07_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_07_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_07_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_07_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工']}
2025-07-31 18:06:40.634 | INFO     | src.gui.main_dialogs:_execute_multi_sheet_import:1479 | 多Sheet导入成功: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_07_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_07_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_07_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_07_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-07', 'data_description': '2025年7月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 07月 > 全部在职人员'}
2025-07-31 18:06:40.634 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:5012 | 开始处理导入结果: {'success': True, 'results': {'离休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_retired_employees 保存 2 条数据。', 'table_name': 'salary_data_2025_07_retired_employees', 'records': 2}, '退休人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_pension_employees 保存 13 条数据。', 'table_name': 'salary_data_2025_07_pension_employees', 'records': 13}, '全部在职人员工资表': {'success': True, 'message': '成功向表 salary_data_2025_07_active_employees 保存 1396 条数据。', 'table_name': 'salary_data_2025_07_active_employees', 'records': 1396}, 'A岗职工': {'success': True, 'message': '成功向表 salary_data_2025_07_a_grade_employees 保存 62 条数据。', 'table_name': 'salary_data_2025_07_a_grade_employees', 'records': 62}}, 'total_records': 1473, 'import_stats': {'total_sheets': 4, 'processed_sheets': 4, 'total_records': 1473, 'failed_records': 0, 'validation_errors': 0}, 'file_info': {'path': 'C:\\test\\salary_changes\\salary_changes\\data\\工资表\\2025年5月份正式工工资（报财务)  最终版.xls', 'name': '2025年5月份正式工工资（报财务)  最终版.xls', 'size_mb': 1.1650390625, 'extension': '.xls', 'is_valid': True, 'error_message': None}, 'processed_sheets': ['离休人员工资表', '退休人员工资表', '全部在职人员工资表', 'A岗职工'], 'import_mode': 'multi_sheet', 'strategy': 'separate_tables', 'data_period': '2025-07', 'data_description': '2025年7月工资数据', 'file_path': 'C:/test/salary_changes/salary_changes/data/工资表/2025年5月份正式工工资（报财务)  最终版.xls', 'target_path': '工资表 > 2025年 > 07月 > 全部在职人员'}
2025-07-31 18:06:40.650 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:5023 | 导入模式: multi_sheet, 目标路径: '工资表 > 2025年 > 07月 > 全部在职人员'
2025-07-31 18:06:40.650 | INFO     | src.gui.prototype.prototype_main_window:_handle_data_imported:5041 | 接收到导入数据, 来源: 未知来源, 目标路径: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-31 18:06:41.450 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:5129 | 检查是否需要更新导航面板: ['工资表', '2025年', '07月', '全部在职人员']
2025-07-31 18:06:41.451 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:5133 | 检测到工资数据导入，开始刷新导航面板
2025-07-31 18:06:41.454 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:5137 | 使用强制刷新方法
2025-07-31 18:06:41.455 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1553 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-07-31 18:06:41.456 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1561 | 找到工资表节点: 💰 工资表
2025-07-31 18:06:41.466 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 4 个匹配类型 'salary_data' 的表
2025-07-31 18:06:41.467 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1646 | 创建年份节点: 2025年，包含 1 个月份
2025-07-31 18:06:41.468 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1666 | 工资数据导航已强制刷新: 1 个年份, 1 个月份
2025-07-31 18:06:41.468 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1669 | 默认路径: 工资表 > 2025年 > 7月 > 全部在职人员
2025-07-31 18:06:41.469 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1708 | force_refresh_salary_data 执行完成
2025-07-31 18:06:41.470 | INFO     | src.gui.prototype.prototype_main_window:_update_navigation_if_needed:5142 | 将在1500ms后导航到: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-31 18:06:41.471 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:5209 | 尝试导航到新导入的路径: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-31 18:06:41.471 | INFO     | src.gui.prototype.prototype_main_window:_navigate_to_imported_path:5214 | 已成功导航到新导入的路径: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-31 18:06:41.473 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:6734 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '07月', '全部在职人员'] -> salary_data_2025_07_active_employees
2025-07-31 18:06:41.473 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:5302 | [数据流追踪] 开始智能数据显示刷新: salary_data_2025_07_active_employees
2025-07-31 18:06:41.480 | INFO     | src.core.smart_pagination_strategy:__init__:47 | 智能分页策略管理器初始化完成
2025-07-31 18:06:41.480 | INFO     | src.core.smart_pagination_strategy:should_use_pagination:68 | [数据流追踪] 分页决策输入: 总记录数=1396, 页面大小=50, 用户偏好=None
2025-07-31 18:06:41.481 | INFO     | src.core.smart_pagination_strategy:should_use_pagination:93 | [数据流追踪] 分页决策输出: 策略=pagination, 原因=大数据集(1396条)分28页显示, 预期性能=550ms, 决策耗时=0ms
2025-07-31 18:06:41.482 | INFO     | src.gui.prototype.prototype_main_window:_refresh_current_data_display:5324 | [数据流追踪] 智能分页策略决策: pagination, 原因=大数据集(1396条)分28页显示, 预期性能=550ms
2025-07-31 18:06:41.483 | INFO     | src.gui.prototype.prototype_main_window:_execute_pagination_mode:5398 | [数据流追踪] 执行分页显示模式: salary_data_2025_07_active_employees, 1396条记录
2025-07-31 18:06:41.484 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:6251 | 使用分页模式加载 salary_data_2025_07_active_employees，第1页，每页50条
2025-07-31 18:06:41.486 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:6338 | [数据流追踪] 优化缓存模块未找到: No module named 'cache_optimization_fix'，使用标准缓存机制
2025-07-31 18:06:41.487 | INFO     | src.gui.prototype.prototype_main_window:_load_data_with_pagination:6380 | 缓存未命中，从数据库加载: salary_data_2025_07_active_employees 第1页
2025-07-31 18:06:41.488 | INFO     | src.gui.prototype.prototype_main_window:_execute_pagination_mode:5416 | [数据流追踪] 分页显示模式: 1396条记录分28页显示
2025-07-31 18:06:41.488 | INFO     | src.gui.prototype.prototype_main_window:run:130 | 开始加载表 salary_data_2025_07_active_employees 第1页数据，每页50条
2025-07-31 18:06:41.489 | INFO     | src.gui.prototype.prototype_main_window:_schedule_safe_navigation:5187 | 同步导航完成: 工资表 > 2025年 > 07月 > 全部在职人员
2025-07-31 18:06:41.490 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-07-31 18:06:41.497 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" LIMIT 50 OFFSET 0
2025-07-31 18:06:41.503 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19990089.0', '20161565.0', '20191782.0', '20151515.0', '20181640.0']
2025-07-31 18:06:41.512 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-07-31 18:06:41.513 | INFO     | src.gui.prototype.prototype_main_window:run:156 | 使用排序查询: 0 个排序列
2025-07-31 18:06:41.514 | INFO     | src.gui.prototype.prototype_main_window:run:187 | 原始数据: 50行, 28列
2025-07-31 18:06:41.515 | INFO     | src.gui.prototype.prototype_main_window:run:194 | 开始应用字段映射
2025-07-31 18:06:41.516 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:5823 | 开始统一字段处理: salary_data_2025_07_active_employees, 原始列数: 28
2025-07-31 18:06:41.594 | INFO     | src.modules.format_management.field_registry:__init__:84 | 🏷️ [字段注册] 字段注册系统初始化: state/data/field_mappings.json
2025-07-31 18:06:41.594 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-07-31 18:06:41.598 | INFO     | src.modules.format_management.unified_format_manager:_setup_event_listeners:686 | 🎯 [事件驱动] 事件监听器设置完成
2025-07-31 18:06:41.601 | INFO     | src.modules.format_management.unified_format_manager:_sync_to_state_manager:701 | 🎯 [统一状态管理] 状态同步完成
2025-07-31 18:06:41.602 | INFO     | src.modules.format_management.format_config:load_config:279 | 🔧 [格式配置] 配置文件不存在，创建默认配置
2025-07-31 18:06:41.605 | INFO     | src.modules.format_management.format_config:save_config:329 | 🔧 [格式配置] 配置文件保存成功
2025-07-31 18:06:41.610 | INFO     | src.modules.format_management.field_registry:load_mappings:345 | 🏷️ [字段注册] 字段映射加载成功
2025-07-31 18:06:41.610 | INFO     | src.modules.format_management.unified_format_manager:_initialize_system:146 | 🎯 [统一格式管理] 系统初始化完成
2025-07-31 18:06:41.611 | INFO     | src.modules.format_management.unified_format_manager:__init__:130 | 🎯 [统一格式管理] 统一格式管理器初始化完成（新架构版本）
2025-07-31 18:06:41.624 | INFO     | src.modules.format_management.format_renderer:render_dataframe:141 | 🎯 [格式渲染] 已按display_order排列字段: 28个字段
2025-07-31 18:06:41.624 | INFO     | src.modules.format_management.format_renderer:render_dataframe:143 | 🎨 [格式渲染] DataFrame格式化完成: salary_data_2025_07_active_employees, 行数: 50, 列数: 28
2025-07-31 18:06:41.625 | INFO     | src.modules.format_management.unified_format_manager:format_data:363 | 🎯 [统一格式管理] 数据格式化完成: salary_data_2025_07_active_employees, 行数: 50, 列数: 28
2025-07-31 18:06:41.629 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:06:41.630 | INFO     | src.gui.prototype.prototype_main_window:_apply_unified_field_processing:5876 | 🔧 [字段处理] 统一字段处理完成并缓存: 24个字段
2025-07-31 18:06:41.634 | INFO     | src.gui.prototype.prototype_main_window:run:204 | PaginationWorker - 字段映射成功: 28 -> 24列
2025-07-31 18:06:41.635 | INFO     | src.gui.prototype.prototype_main_window:run:218 | 字段映射成功: 24列
2025-07-31 18:06:41.637 | INFO     | src.gui.prototype.prototype_main_window:run:244 | 最终数据: 50行, 24列, 总记录数: 1396
2025-07-31 18:06:41.639 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:6411 | 分页数据加载成功（数据库）: 50条数据，第1页，总计1396条
2025-07-31 18:06:41.639 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:6445 | 🚀 [性能缓存] 数据已缓存: salary_data_2025_07_active_employees 第1页
2025-07-31 18:06:41.647 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:439 | 正在从表 salary_data_2025_07_active_employees 分页获取数据: 第2页, 每页50条
2025-07-31 18:06:41.647 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:6474 | 🧹 [异步分页] 使用重构后的统一格式化结果: 50行, 24列
2025-07-31 18:06:41.651 | INFO     | src.gui.prototype.prototype_main_window:set_data:728 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-31 18:06:41.655 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated:462 | 成功从表 salary_data_2025_07_active_employees 获取第2页数据: 50 行，总计1396行
2025-07-31 18:06:41.655 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:06:41.669 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089, 薪资=2375
2025-07-31 18:06:41.669 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565, 薪资=1696
2025-07-31 18:06:41.671 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:4558 | 最大可见行数已更新: 1000 -> 50
2025-07-31 18:06:41.672 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-31 18:06:41.688 | INFO     | src.modules.format_management.master_format_manager:_load_format_config:475 | 格式化配置加载成功: C:\test\salary_changes\salary_changes\state\format_config.json
2025-07-31 18:06:41.689 | INFO     | src.modules.format_management.field_registry:__init__:84 | 🏷️ [字段注册] 字段注册系统初始化: C:\test\salary_changes\salary_changes\state\data\field_mappings.json
2025-07-31 18:06:41.690 | INFO     | src.modules.format_management.field_registry:load_mappings:345 | 🏷️ [字段注册] 字段映射加载成功
2025-07-31 18:06:41.695 | INFO     | src.modules.format_management.format_config:load_config:297 | 🔧 [格式配置] 配置文件加载成功
2025-07-31 18:06:41.695 | INFO     | src.modules.format_management.format_renderer:__init__:83 | 🎨 [格式渲染] 格式渲染器初始化完成
2025-07-31 18:06:41.696 | INFO     | src.modules.format_management.master_format_manager:_initialize_unified_format_system:182 | 🔧 [新系统] 统一格式管理系统组件初始化完成
2025-07-31 18:06:41.697 | INFO     | src.modules.format_management.master_format_manager:clear_cache:453 | 🎯 [统一格式化] 格式化缓存已清理
2025-07-31 18:06:41.698 | INFO     | src.modules.format_management.master_format_manager:__init__:156 | 🎯 [统一格式化] 主格式化管理器已初始化（新系统）
2025-07-31 18:06:41.707 | INFO     | src.modules.format_management.format_renderer:render_dataframe:143 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-07-31 18:06:41.707 | WARNING  | src.modules.format_management.master_format_manager:_verify_formatting_completeness:694 | 🔧 [格式化验证] 以下字段格式化不完整: ['人员类别代码', '车补', '补发', '借支', '代扣代存养老保险', '月份']
2025-07-31 18:06:41.708 | WARNING  | src.modules.format_management.master_format_manager:format_table_data:226 | 🎯 [统一格式化] 格式化验证未通过，不标记: active_employees
2025-07-31 18:06:41.709 | INFO     | src.modules.format_management.master_format_manager:format_table_data:232 | 🎯 [统一格式化] 数据格式化完成: active_employees
2025-07-31 18:06:41.712 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2517 | 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-31 18:06:41.717 | INFO     | src.modules.format_management.format_config:load_config:297 | 🔧 [格式配置] 配置文件加载成功
2025-07-31 18:06:41.732 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-07-31 18:06:41.733 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-07-31 18:06:41.743 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时9.9ms, 平均每行0.20ms
2025-07-31 18:06:41.754 | INFO     | src.core.performance_metrics_collector:__init__:74 | 性能度量收集器初始化完成，存储路径: logs\performance_metrics.json
2025-07-31 18:06:41.754 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=9.9ms, 策略=small_dataset
2025-07-31 18:06:41.756 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-07-31 18:06:41.757 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2598 | [数据流追踪] 优化渲染完成: 50行 x 24列, 策略=small_dataset, 耗时=9.9ms, 性能评级=excellent
2025-07-31 18:06:41.758 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 19990089
2025-07-31 18:06:41.759 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 20161565
2025-07-31 18:06:41.760 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 20191782
2025-07-31 18:06:41.764 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 20151515
2025-07-31 18:06:41.765 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 20181640
2025-07-31 18:06:41.766 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-07-31 18:06:41.766 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375
2025-07-31 18:06:41.767 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696
2025-07-31 18:06:41.768 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427
2025-07-31 18:06:41.769 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175
2025-07-31 18:06:41.770 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582
2025-07-31 18:06:41.771 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:472 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-31 18:06:41.772 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 50 行, 24 列
2025-07-31 18:06:41.774 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:409 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_07_active_employees
2025-07-31 18:06:41.780 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:440 | 🔧 [新架构] 为表格 salary_data_2025_07_active_employees 重新加载 28 个字段映射
2025-07-31 18:06:41.780 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2678 | 表格数据设置完成: 50 行, 耗时: 105.7ms
2025-07-31 18:06:41.781 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7115 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-31 18:06:41.782 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7128 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-31 18:06:41.783 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1632 | 🆕 [列宽保存] 未找到表格 salary_data_2025_07_active_employees 的列宽配置
2025-07-31 18:06:41.784 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 50
2025-07-31 18:06:41.784 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:6493 | 🔍 [调试-分页] 即将调用set_pagination_state: 第1页, 记录1-50
2025-07-31 18:06:41.785 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3721 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-07-31 18:06:41.786 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3722 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 1396, 'start_record': 1, 'end_record': 50}
2025-07-31 18:06:41.787 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3723 | 🔍 [表格调试] 当前表格行数: 50
2025-07-31 18:06:41.788 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3724 | 🔍 [表格调试] 当前表格列数: 24
2025-07-31 18:06:41.795 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3726 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-07-31 18:06:41.796 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3752 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-07-31 18:06:41.797 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-07-31 18:06:41.798 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:3831 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-07-31 18:06:41.798 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:6495 | 🔍 [调试-分页] set_pagination_state调用完成
2025-07-31 18:06:41.799 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 1396
2025-07-31 18:06:41.803 | INFO     | src.gui.prototype.prototype_main_window:_on_pagination_data_loaded:6552 | 🆕 [新架构] 分页数据加载完成，已完成渐进式状态迁移
2025-07-31 18:06:42.636 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:7512 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-07-31 18:06:52.146 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1597 | 🔧 [列宽保存修复] 准备保存列宽到文件: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-07-31 18:06:52.146 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1598 | 🔧 [列宽保存修复] 表名: salary_data_2025_07_active_employees, 列数: 24
2025-07-31 18:06:52.146 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1603 | 🔧 [列宽保存修复] 列宽设置保存成功！文件大小: 1901 字节
2025-07-31 18:07:17.948 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4275 | 🔧 [分页请求] 第2页, 表: salary_data_2025_07_active_employees, 上下文已设置
2025-07-31 18:07:17.964 | INFO     | src.core.request_deduplication_manager:__init__:57 | 请求去重管理器初始化完成，默认TTL: 0.3秒
2025-07-31 18:07:17.964 | INFO     | src.core.request_deduplication_manager:__init__:278 | 智能去重管理器初始化完成
2025-07-31 18:07:17.964 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4305 | [数据流追踪] 分页请求通过去重检查: salary_data_2025_07_active_employees, 第2页, pagination
2025-07-31 18:07:17.964 | INFO     | src.services.table_data_service:load_table_data:347 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 2
2025-07-31 18:07:17.964 | INFO     | src.core.unified_data_request_manager:request_table_data:203 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: initial_load
2025-07-31 18:07:17.964 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第2页, 每页50条, 排序=0列
2025-07-31 18:07:17.964 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" LIMIT 50 OFFSET 50
2025-07-31 18:07:17.964 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['20171604.0', '20181638.0', '19930191.0', '20181669.0', '20191722.0']
2025-07-31 18:07:17.964 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第2页数据（含排序）: 50 行，总计1396行
2025-07-31 18:07:17.980 | INFO     | src.core.unified_data_request_manager:request_table_data:252 | 数据请求处理完成: 28字段, 50行, 耗时15.7ms
2025-07-31 18:07:17.980 | INFO     | temp.format_manager_singleton_fix:__init__:120 | [单例修复] 主格式化管理器单例初始化完成
2025-07-31 18:07:17.980 | INFO     | src.services.table_data_service:load_table_data:434 | [性能优化] 使用单例格式化管理器，避免重复初始化
2025-07-31 18:07:17.980 | INFO     | src.services.table_data_service:load_table_data:452 | [统一格式化] 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-31 18:07:17.995 | INFO     | src.services.table_data_service:load_table_data:489 | [修复数据发布] 数据加载成功，发布更新事件: salary_data_2025_07_active_employees, 50行
2025-07-31 18:07:17.995 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3317 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-31 18:07:17.995 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3342 | 数据内容: 50行 x 28列
2025-07-31 18:07:17.995 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3368 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_07_active_employees, 50行
2025-07-31 18:07:17.995 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:07:17.995 | INFO     | src.gui.prototype.prototype_main_window:set_data:728 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-31 18:07:17.995 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:07:17.995 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=20171604.0, 薪资=1427.0
2025-07-31 18:07:17.995 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20181638.0, 薪资=1515.0
2025-07-31 18:07:17.995 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:4558 | 最大可见行数已更新: 50 -> 50
2025-07-31 18:07:17.995 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-31 18:07:18.027 | INFO     | src.modules.format_management.format_renderer:render_dataframe:143 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-07-31 18:07:18.027 | WARNING  | src.modules.format_management.master_format_manager:_verify_formatting_completeness:694 | 🔧 [格式化验证] 以下字段格式化不完整: ['人员类别代码', '车补', '补发', '借支', '月份']
2025-07-31 18:07:18.027 | WARNING  | src.modules.format_management.master_format_manager:format_table_data:226 | 🎯 [统一格式化] 格式化验证未通过，不标记: active_employees
2025-07-31 18:07:18.027 | INFO     | src.modules.format_management.master_format_manager:format_table_data:232 | 🎯 [统一格式化] 数据格式化完成: active_employees
2025-07-31 18:07:18.027 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2517 | 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-31 18:07:18.043 | WARNING  | src.modules.format_management.master_format_manager:_format_department_code_value:386 | 🔧 [部门代码格式化] 格式化失败 -: could not convert string to float: '-'
2025-07-31 18:07:18.043 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-07-31 18:07:18.043 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-07-31 18:07:18.062 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时19.1ms, 平均每行0.38ms
2025-07-31 18:07:18.062 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=19.1ms, 策略=small_dataset
2025-07-31 18:07:18.062 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-07-31 18:07:18.062 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2598 | [数据流追踪] 优化渲染完成: 50行 x 24列, 策略=small_dataset, 耗时=19.1ms, 性能评级=excellent
2025-07-31 18:07:18.062 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 20171604
2025-07-31 18:07:18.062 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 20181638
2025-07-31 18:07:18.062 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 19930191
2025-07-31 18:07:18.062 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 20181669
2025-07-31 18:07:18.062 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 20191722
2025-07-31 18:07:18.062 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-07-31 18:07:18.062 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[0]: 工号=20171604, 薪资=1427
2025-07-31 18:07:18.073 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[1]: 工号=20181638, 薪资=1515
2025-07-31 18:07:18.073 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[2]: 工号=19930191, 薪资=3391
2025-07-31 18:07:18.073 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[3]: 工号=20181669, 薪资=1515
2025-07-31 18:07:18.073 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[4]: 工号=20191722, 薪资=1515
2025-07-31 18:07:18.073 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:472 | 可见行数据顺序: ['20171604', '20181638', '19930191', '20181669', '20191722']
2025-07-31 18:07:18.073 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 50 行, 24 列
2025-07-31 18:07:18.073 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2678 | 表格数据设置完成: 50 行, 耗时: 78.0ms
2025-07-31 18:07:18.073 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7115 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-31 18:07:18.073 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7128 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-31 18:07:18.073 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1657 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-31 18:07:18.073 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 50
2025-07-31 18:07:18.084 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3400 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-07-31 18:07:18.095 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 1396
2025-07-31 18:07:18.096 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3414 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-07-31 18:07:18.097 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3433 | 🔧 [分页修复] 分页状态: 当前页=2, 总页数=28, 总记录数=1396
2025-07-31 18:07:18.098 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3439 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=True
2025-07-31 18:07:18.099 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3450 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第2页, 记录51-100
2025-07-31 18:07:18.099 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3721 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-07-31 18:07:18.100 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3722 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 2, 'page_size': 50, 'total_records': 1396, 'start_record': 51, 'end_record': 100}
2025-07-31 18:07:18.101 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3723 | 🔍 [表格调试] 当前表格行数: 50
2025-07-31 18:07:18.102 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3724 | 🔍 [表格调试] 当前表格列数: 24
2025-07-31 18:07:18.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3726 | 🔧 [分页行号修复] 接收分页状态: 第2页, 记录51-100
2025-07-31 18:07:18.108 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3752 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-07-31 18:07:18.109 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 51, 共 50 行
2025-07-31 18:07:18.111 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:3831 | 🔧 [分页行号修复] 强制更新完成: 起始记录51, 共50行
2025-07-31 18:07:18.112 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3452 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-07-31 18:07:18.113 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3466 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-07-31 18:07:18.114 | INFO     | src.services.table_data_service:load_table_data:510 | [修复数据发布] 数据更新事件已发布
2025-07-31 18:07:18.114 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4320 | 🚫 [用户要求] 不显示加载条，直接处理数据: salary_data_2025_07_active_employees 第2页
2025-07-31 18:07:18.115 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4323 | 🔧 [根本修复] 分页数据获取成功: 第2页, 50行
2025-07-31 18:07:18.116 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4329 | 🔧 [根本修复] 获取数据的前3个工号: ['无工号']
2025-07-31 18:07:18.117 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4337 | [数据流追踪] 分页数据请求成功: 第2页, 50行
2025-07-31 18:07:18.123 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4344 | 🔧 [关键修复] 分页处理标志已清除，允许UI更新: 第2页
2025-07-31 18:07:18.124 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4349 | 🧹 [智能修复] 数据返回完成，缓存状态: False, 第2页
2025-07-31 18:07:18.125 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 1396
2025-07-31 18:07:18.125 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4364 | 🔧 [简化分页处理] 分页处理完成，等待事件机制更新UI: 第2页
2025-07-31 18:07:18.126 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4414 | 🚫 [根本修复] 分页处理完成，标志已清除: 第2页
2025-07-31 18:07:18.127 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4275 | 🔧 [分页请求] 第2页, 表: salary_data_2025_07_active_employees, 上下文已设置
2025-07-31 18:07:18.128 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4305 | [数据流追踪] 分页请求通过去重检查: salary_data_2025_07_active_employees, 第2页, pagination
2025-07-31 18:07:18.128 | INFO     | src.services.table_data_service:load_table_data:347 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 2
2025-07-31 18:07:18.129 | INFO     | src.services.table_data_service:load_table_data:370 | [缓存命中] 使用缓存数据: salary_data_2025_07_active_employees 第2页
2025-07-31 18:07:18.130 | INFO     | src.services.table_data_service:load_table_data:376 | [根本修复] 缓存命中，发布数据更新事件: salary_data_2025_07_active_employees, 50行
2025-07-31 18:07:18.136 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3317 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-31 18:07:18.137 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3342 | 数据内容: 50行 x 28列
2025-07-31 18:07:18.138 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3368 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_07_active_employees, 50行
2025-07-31 18:07:18.141 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:07:18.142 | INFO     | src.gui.prototype.prototype_main_window:set_data:728 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-31 18:07:18.144 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:07:18.149 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=20171604.0, 薪资=1427.0
2025-07-31 18:07:18.150 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20181638.0, 薪资=1515.0
2025-07-31 18:07:18.151 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:4558 | 最大可见行数已更新: 50 -> 50
2025-07-31 18:07:18.152 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-31 18:07:18.159 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2517 | 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-31 18:07:18.171 | WARNING  | src.modules.format_management.master_format_manager:_format_department_code_value:386 | 🔧 [部门代码格式化] 格式化失败 -: could not convert string to float: '-'
2025-07-31 18:07:18.179 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-07-31 18:07:18.179 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-07-31 18:07:18.189 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时9.9ms, 平均每行0.20ms
2025-07-31 18:07:18.189 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=9.9ms, 策略=small_dataset
2025-07-31 18:07:18.190 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-07-31 18:07:18.190 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2598 | [数据流追踪] 优化渲染完成: 50行 x 24列, 策略=small_dataset, 耗时=9.9ms, 性能评级=excellent
2025-07-31 18:07:18.192 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 20171604
2025-07-31 18:07:18.193 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 20181638
2025-07-31 18:07:18.193 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 19930191
2025-07-31 18:07:18.194 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 20181669
2025-07-31 18:07:18.199 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 20191722
2025-07-31 18:07:18.199 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-07-31 18:07:18.200 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[0]: 工号=20171604, 薪资=1427
2025-07-31 18:07:18.201 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[1]: 工号=20181638, 薪资=1515
2025-07-31 18:07:18.201 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[2]: 工号=19930191, 薪资=3391
2025-07-31 18:07:18.202 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[3]: 工号=20181669, 薪资=1515
2025-07-31 18:07:18.203 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[4]: 工号=20191722, 薪资=1515
2025-07-31 18:07:18.203 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:472 | 可见行数据顺序: ['20171604', '20181638', '19930191', '20181669', '20191722']
2025-07-31 18:07:18.204 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 50 行, 24 列
2025-07-31 18:07:18.206 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2678 | 表格数据设置完成: 50 行, 耗时: 59.5ms
2025-07-31 18:07:18.212 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7115 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-31 18:07:18.213 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7128 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-31 18:07:18.214 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1657 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-31 18:07:18.215 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 50
2025-07-31 18:07:18.216 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3400 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-07-31 18:07:18.217 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 1396
2025-07-31 18:07:18.218 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3414 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-07-31 18:07:18.218 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3433 | 🔧 [分页修复] 分页状态: 当前页=2, 总页数=28, 总记录数=1396
2025-07-31 18:07:18.219 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3439 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=True
2025-07-31 18:07:18.220 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3450 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第2页, 记录51-100
2025-07-31 18:07:18.221 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3721 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-07-31 18:07:18.228 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3722 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 2, 'page_size': 50, 'total_records': 1396, 'start_record': 51, 'end_record': 100}
2025-07-31 18:07:18.229 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3723 | 🔍 [表格调试] 当前表格行数: 50
2025-07-31 18:07:18.230 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3724 | 🔍 [表格调试] 当前表格列数: 24
2025-07-31 18:07:18.230 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3726 | 🔧 [分页行号修复] 接收分页状态: 第2页, 记录51-100
2025-07-31 18:07:18.231 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3752 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-07-31 18:07:18.232 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 51, 共 50 行
2025-07-31 18:07:18.233 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:3831 | 🔧 [分页行号修复] 强制更新完成: 起始记录51, 共50行
2025-07-31 18:07:18.234 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3452 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-07-31 18:07:18.234 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3466 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-07-31 18:07:18.235 | INFO     | src.services.table_data_service:load_table_data:398 | [根本修复] 缓存数据更新事件已发布，UI应该能正常更新
2025-07-31 18:07:18.235 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4320 | 🚫 [用户要求] 不显示加载条，直接处理数据: salary_data_2025_07_active_employees 第2页
2025-07-31 18:07:18.244 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4323 | 🔧 [根本修复] 分页数据获取成功: 第2页, 50行
2025-07-31 18:07:18.244 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4329 | 🔧 [根本修复] 获取数据的前3个工号: ['无工号']
2025-07-31 18:07:18.245 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4337 | [数据流追踪] 分页数据请求成功: 第2页, 50行
2025-07-31 18:07:18.245 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4344 | 🔧 [关键修复] 分页处理标志已清除，允许UI更新: 第2页
2025-07-31 18:07:18.246 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4349 | 🧹 [智能修复] 数据返回完成，缓存状态: True, 第2页
2025-07-31 18:07:18.247 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 1396
2025-07-31 18:07:18.247 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4364 | 🔧 [简化分页处理] 分页处理完成，等待事件机制更新UI: 第2页
2025-07-31 18:07:18.248 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4414 | 🚫 [根本修复] 分页处理完成，标志已清除: 第2页
2025-07-31 18:07:18.249 | INFO     | src.gui.widgets.pagination_widget:set_current_page:570 | 页码切换到: 2
2025-07-31 18:07:18.526 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1597 | 🔧 [列宽保存修复] 准备保存列宽到文件: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-07-31 18:07:18.527 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1598 | 🔧 [列宽保存修复] 表名: salary_data_2025_07_active_employees, 列数: 24
2025-07-31 18:07:18.531 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1603 | 🔧 [列宽保存修复] 列宽设置保存成功！文件大小: 1900 字节
2025-07-31 18:07:19.983 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4275 | 🔧 [分页请求] 第3页, 表: salary_data_2025_07_active_employees, 上下文已设置
2025-07-31 18:07:19.983 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4305 | [数据流追踪] 分页请求通过去重检查: salary_data_2025_07_active_employees, 第3页, pagination
2025-07-31 18:07:19.983 | INFO     | src.services.table_data_service:load_table_data:347 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 3
2025-07-31 18:07:19.983 | INFO     | src.core.unified_data_request_manager:request_table_data:203 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: initial_load
2025-07-31 18:07:19.983 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第3页, 每页50条, 排序=0列
2025-07-31 18:07:19.983 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" LIMIT 50 OFFSET 100
2025-07-31 18:07:19.999 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['20181658.0', '20181671.0', '20191701.0', '20191777.0', '20201018.0']
2025-07-31 18:07:19.999 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第3页数据（含排序）: 50 行，总计1396行
2025-07-31 18:07:19.999 | INFO     | src.core.unified_data_request_manager:request_table_data:252 | 数据请求处理完成: 28字段, 50行, 耗时15.7ms
2025-07-31 18:07:19.999 | INFO     | src.services.table_data_service:load_table_data:434 | [性能优化] 使用单例格式化管理器，避免重复初始化
2025-07-31 18:07:19.999 | INFO     | src.services.table_data_service:load_table_data:452 | [统一格式化] 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-31 18:07:19.999 | INFO     | src.services.table_data_service:load_table_data:489 | [修复数据发布] 数据加载成功，发布更新事件: salary_data_2025_07_active_employees, 50行
2025-07-31 18:07:19.999 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3317 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-31 18:07:19.999 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3342 | 数据内容: 50行 x 28列
2025-07-31 18:07:19.999 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3368 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_07_active_employees, 50行
2025-07-31 18:07:19.999 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:07:19.999 | INFO     | src.gui.prototype.prototype_main_window:set_data:728 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-31 18:07:19.999 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:07:20.014 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=20181658.0, 薪资=1251.0
2025-07-31 18:07:20.014 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20181671.0, 薪资=1251.0
2025-07-31 18:07:20.014 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:4558 | 最大可见行数已更新: 50 -> 50
2025-07-31 18:07:20.014 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-31 18:07:20.031 | INFO     | src.modules.format_management.format_renderer:render_dataframe:143 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-07-31 18:07:20.031 | WARNING  | src.modules.format_management.master_format_manager:_verify_formatting_completeness:694 | 🔧 [格式化验证] 以下字段格式化不完整: ['人员类别代码', '车补', '补发', '借支', '月份']
2025-07-31 18:07:20.031 | WARNING  | src.modules.format_management.master_format_manager:format_table_data:226 | 🎯 [统一格式化] 格式化验证未通过，不标记: active_employees
2025-07-31 18:07:20.031 | INFO     | src.modules.format_management.master_format_manager:format_table_data:232 | 🎯 [统一格式化] 数据格式化完成: active_employees
2025-07-31 18:07:20.031 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2517 | 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-31 18:07:20.046 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-07-31 18:07:20.046 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-07-31 18:07:20.061 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时15.7ms, 平均每行0.31ms
2025-07-31 18:07:20.061 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=15.7ms, 策略=small_dataset
2025-07-31 18:07:20.061 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-07-31 18:07:20.061 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2598 | [数据流追踪] 优化渲染完成: 50行 x 24列, 策略=small_dataset, 耗时=15.7ms, 性能评级=excellent
2025-07-31 18:07:20.061 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 20181658
2025-07-31 18:07:20.061 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 20181671
2025-07-31 18:07:20.061 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 20191701
2025-07-31 18:07:20.061 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 20191777
2025-07-31 18:07:20.061 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 20201018
2025-07-31 18:07:20.061 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-07-31 18:07:20.061 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[0]: 工号=20181658, 薪资=1251
2025-07-31 18:07:20.061 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[1]: 工号=20181671, 薪资=1251
2025-07-31 18:07:20.077 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[2]: 工号=20191701, 薪资=1427
2025-07-31 18:07:20.077 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[3]: 工号=20191777, 薪资=2475
2025-07-31 18:07:20.077 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[4]: 工号=20201018, 薪资=1087
2025-07-31 18:07:20.077 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:472 | 可见行数据顺序: ['20181658', '20181671', '20191701', '20191777', '20201018']
2025-07-31 18:07:20.077 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 50 行, 24 列
2025-07-31 18:07:20.077 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2678 | 表格数据设置完成: 50 行, 耗时: 63.2ms
2025-07-31 18:07:20.077 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7115 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-31 18:07:20.077 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7128 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-31 18:07:20.077 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1657 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-31 18:07:20.077 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 50
2025-07-31 18:07:20.093 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3400 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-07-31 18:07:20.098 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 1396
2025-07-31 18:07:20.099 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3414 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-07-31 18:07:20.100 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3433 | 🔧 [分页修复] 分页状态: 当前页=3, 总页数=28, 总记录数=1396
2025-07-31 18:07:20.101 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3439 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=True
2025-07-31 18:07:20.102 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3450 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第3页, 记录101-150
2025-07-31 18:07:20.103 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3721 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-07-31 18:07:20.104 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3722 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 3, 'page_size': 50, 'total_records': 1396, 'start_record': 101, 'end_record': 150}
2025-07-31 18:07:20.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3723 | 🔍 [表格调试] 当前表格行数: 50
2025-07-31 18:07:20.105 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3724 | 🔍 [表格调试] 当前表格列数: 24
2025-07-31 18:07:20.106 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3726 | 🔧 [分页行号修复] 接收分页状态: 第3页, 记录101-150
2025-07-31 18:07:20.107 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3752 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-07-31 18:07:20.113 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 101, 共 50 行
2025-07-31 18:07:20.114 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:3831 | 🔧 [分页行号修复] 强制更新完成: 起始记录101, 共50行
2025-07-31 18:07:20.115 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3452 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-07-31 18:07:20.116 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3466 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-07-31 18:07:20.117 | INFO     | src.services.table_data_service:load_table_data:510 | [修复数据发布] 数据更新事件已发布
2025-07-31 18:07:20.117 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4320 | 🚫 [用户要求] 不显示加载条，直接处理数据: salary_data_2025_07_active_employees 第3页
2025-07-31 18:07:20.118 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4323 | 🔧 [根本修复] 分页数据获取成功: 第3页, 50行
2025-07-31 18:07:20.119 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4329 | 🔧 [根本修复] 获取数据的前3个工号: ['无工号']
2025-07-31 18:07:20.120 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4337 | [数据流追踪] 分页数据请求成功: 第3页, 50行
2025-07-31 18:07:20.121 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4344 | 🔧 [关键修复] 分页处理标志已清除，允许UI更新: 第3页
2025-07-31 18:07:20.128 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4349 | 🧹 [智能修复] 数据返回完成，缓存状态: False, 第3页
2025-07-31 18:07:20.129 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 1396
2025-07-31 18:07:20.130 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4364 | 🔧 [简化分页处理] 分页处理完成，等待事件机制更新UI: 第3页
2025-07-31 18:07:20.130 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4414 | 🚫 [根本修复] 分页处理完成，标志已清除: 第3页
2025-07-31 18:07:20.131 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4275 | 🔧 [分页请求] 第3页, 表: salary_data_2025_07_active_employees, 上下文已设置
2025-07-31 18:07:20.132 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4305 | [数据流追踪] 分页请求通过去重检查: salary_data_2025_07_active_employees, 第3页, pagination
2025-07-31 18:07:20.133 | INFO     | src.services.table_data_service:load_table_data:347 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 3
2025-07-31 18:07:20.134 | INFO     | src.services.table_data_service:load_table_data:370 | [缓存命中] 使用缓存数据: salary_data_2025_07_active_employees 第3页
2025-07-31 18:07:20.134 | INFO     | src.services.table_data_service:load_table_data:376 | [根本修复] 缓存命中，发布数据更新事件: salary_data_2025_07_active_employees, 50行
2025-07-31 18:07:20.135 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3317 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-31 18:07:20.142 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3342 | 数据内容: 50行 x 28列
2025-07-31 18:07:20.143 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3368 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_07_active_employees, 50行
2025-07-31 18:07:20.145 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:07:20.146 | INFO     | src.gui.prototype.prototype_main_window:set_data:728 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-31 18:07:20.148 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:07:20.155 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=20181658.0, 薪资=1251.0
2025-07-31 18:07:20.155 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20181671.0, 薪资=1251.0
2025-07-31 18:07:20.156 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:4558 | 最大可见行数已更新: 50 -> 50
2025-07-31 18:07:20.157 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-31 18:07:20.164 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2517 | 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-31 18:07:20.183 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-07-31 18:07:20.183 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-07-31 18:07:20.191 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时7.8ms, 平均每行0.16ms
2025-07-31 18:07:20.191 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=7.8ms, 策略=small_dataset
2025-07-31 18:07:20.192 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-07-31 18:07:20.193 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2598 | [数据流追踪] 优化渲染完成: 50行 x 24列, 策略=small_dataset, 耗时=7.8ms, 性能评级=excellent
2025-07-31 18:07:20.193 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 20181658
2025-07-31 18:07:20.194 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 20181671
2025-07-31 18:07:20.194 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 20191701
2025-07-31 18:07:20.195 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 20191777
2025-07-31 18:07:20.197 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 20201018
2025-07-31 18:07:20.198 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-07-31 18:07:20.199 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[0]: 工号=20181658, 薪资=1251
2025-07-31 18:07:20.204 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[1]: 工号=20181671, 薪资=1251
2025-07-31 18:07:20.205 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[2]: 工号=20191701, 薪资=1427
2025-07-31 18:07:20.205 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[3]: 工号=20191777, 薪资=2475
2025-07-31 18:07:20.206 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[4]: 工号=20201018, 薪资=1087
2025-07-31 18:07:20.207 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:472 | 可见行数据顺序: ['20181658', '20181671', '20191701', '20191777', '20201018']
2025-07-31 18:07:20.208 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 50 行, 24 列
2025-07-31 18:07:20.210 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2678 | 表格数据设置完成: 50 行, 耗时: 57.8ms
2025-07-31 18:07:20.210 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7115 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-31 18:07:20.211 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7128 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-31 18:07:20.214 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1657 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-31 18:07:20.220 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 50
2025-07-31 18:07:20.220 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3400 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-07-31 18:07:20.221 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 1396
2025-07-31 18:07:20.222 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3414 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-07-31 18:07:20.223 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3433 | 🔧 [分页修复] 分页状态: 当前页=3, 总页数=28, 总记录数=1396
2025-07-31 18:07:20.224 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3439 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=True
2025-07-31 18:07:20.224 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3450 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第3页, 记录101-150
2025-07-31 18:07:20.225 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3721 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-07-31 18:07:20.227 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3722 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 3, 'page_size': 50, 'total_records': 1396, 'start_record': 101, 'end_record': 150}
2025-07-31 18:07:20.227 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3723 | 🔍 [表格调试] 当前表格行数: 50
2025-07-31 18:07:20.228 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3724 | 🔍 [表格调试] 当前表格列数: 24
2025-07-31 18:07:20.234 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3726 | 🔧 [分页行号修复] 接收分页状态: 第3页, 记录101-150
2025-07-31 18:07:20.235 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3752 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-07-31 18:07:20.236 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 101, 共 50 行
2025-07-31 18:07:20.237 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:3831 | 🔧 [分页行号修复] 强制更新完成: 起始记录101, 共50行
2025-07-31 18:07:20.238 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3452 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-07-31 18:07:20.239 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3466 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-07-31 18:07:20.240 | INFO     | src.services.table_data_service:load_table_data:398 | [根本修复] 缓存数据更新事件已发布，UI应该能正常更新
2025-07-31 18:07:20.240 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4320 | 🚫 [用户要求] 不显示加载条，直接处理数据: salary_data_2025_07_active_employees 第3页
2025-07-31 18:07:20.241 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4323 | 🔧 [根本修复] 分页数据获取成功: 第3页, 50行
2025-07-31 18:07:20.242 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4329 | 🔧 [根本修复] 获取数据的前3个工号: ['无工号']
2025-07-31 18:07:20.248 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4337 | [数据流追踪] 分页数据请求成功: 第3页, 50行
2025-07-31 18:07:20.249 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4344 | 🔧 [关键修复] 分页处理标志已清除，允许UI更新: 第3页
2025-07-31 18:07:20.250 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4349 | 🧹 [智能修复] 数据返回完成，缓存状态: True, 第3页
2025-07-31 18:07:20.251 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 1396
2025-07-31 18:07:20.252 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4364 | 🔧 [简化分页处理] 分页处理完成，等待事件机制更新UI: 第3页
2025-07-31 18:07:20.253 | INFO     | src.gui.prototype.prototype_main_window:_on_page_changed_new_architecture:4414 | 🚫 [根本修复] 分页处理完成，标志已清除: 第3页
2025-07-31 18:07:20.254 | INFO     | src.gui.widgets.pagination_widget:set_current_page:570 | 页码切换到: 3
2025-07-31 18:07:20.526 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1597 | 🔧 [列宽保存修复] 准备保存列宽到文件: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-07-31 18:07:20.527 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1598 | 🔧 [列宽保存修复] 表名: salary_data_2025_07_active_employees, 列数: 24
2025-07-31 18:07:20.531 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1603 | 🔧 [列宽保存修复] 列宽设置保存成功！文件大小: 1899 字节
2025-07-31 18:07:23.410 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表']
2025-07-31 18:07:23.410 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:6034 | 导航变化: 工资表 > 2025年
2025-07-31 18:07:23.410 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8517 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_07_active_employees -> None
2025-07-31 18:07:23.410 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8533 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-07-31 18:07:23.410 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:5941 | 🔧 [缓存清理] 清理了所有 1 个字段处理缓存条目
2025-07-31 18:07:23.410 | WARNING  | src.gui.prototype.prototype_main_window:set_data:688 | 尝试设置空数据，将显示提示信息。
2025-07-31 18:07:23.410 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6895 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-07-31 18:07:23.410 | WARNING  | src.gui.prototype.prototype_main_window:set_data:672 | 🔧 [P0-2] 检测到递归设置数据，跳过以防止表头重影
2025-07-31 18:07:23.425 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6910 | 已显示标准空表格，表头数量: 22
2025-07-31 18:07:23.425 | INFO     | src.gui.widgets.pagination_widget:reset:620 | 分页状态已重置
2025-07-31 18:07:23.425 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6895 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-07-31 18:07:23.425 | WARNING  | src.gui.prototype.prototype_main_window:set_data:708 | 尝试设置空数据，将显示提示信息。
2025-07-31 18:07:23.425 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6895 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-07-31 18:07:23.425 | WARNING  | src.gui.prototype.prototype_main_window:set_data:672 | 🔧 [P0-2] 检测到递归设置数据，跳过以防止表头重影
2025-07-31 18:07:23.425 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6910 | 已显示标准空表格，表头数量: 22
2025-07-31 18:07:23.425 | INFO     | src.gui.widgets.pagination_widget:reset:620 | 分页状态已重置
2025-07-31 18:07:23.425 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6910 | 已显示标准空表格，表头数量: 22
2025-07-31 18:07:23.425 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:896 | 导航选择: 工资表 > 2025年
2025-07-31 18:07:24.957 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 7月', '工资表']
2025-07-31 18:07:24.957 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:6034 | 导航变化: 工资表 > 2025年 > 7月
2025-07-31 18:07:24.957 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8517 | 🚫 [用户要求] 表格切换不显示加载条: None -> None
2025-07-31 18:07:24.957 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8533 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-07-31 18:07:24.957 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:5941 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-07-31 18:07:24.974 | WARNING  | src.gui.prototype.prototype_main_window:set_data:688 | 尝试设置空数据，将显示提示信息。
2025-07-31 18:07:24.974 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6895 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-07-31 18:07:24.974 | WARNING  | src.gui.prototype.prototype_main_window:set_data:672 | 🔧 [P0-2] 检测到递归设置数据，跳过以防止表头重影
2025-07-31 18:07:24.974 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6910 | 已显示标准空表格，表头数量: 22
2025-07-31 18:07:24.974 | INFO     | src.gui.widgets.pagination_widget:reset:620 | 分页状态已重置
2025-07-31 18:07:24.974 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6895 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-07-31 18:07:24.974 | WARNING  | src.gui.prototype.prototype_main_window:set_data:708 | 尝试设置空数据，将显示提示信息。
2025-07-31 18:07:24.974 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6895 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-07-31 18:07:24.974 | WARNING  | src.gui.prototype.prototype_main_window:set_data:672 | 🔧 [P0-2] 检测到递归设置数据，跳过以防止表头重影
2025-07-31 18:07:24.974 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6910 | 已显示标准空表格，表头数量: 22
2025-07-31 18:07:24.974 | INFO     | src.gui.widgets.pagination_widget:reset:620 | 分页状态已重置
2025-07-31 18:07:24.974 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6910 | 已显示标准空表格，表头数量: 22
2025-07-31 18:07:24.974 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:896 | 导航选择: 工资表 > 2025年 > 7月
2025-07-31 18:07:28.191 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 7月', '工资表 > 2025年 > 7月 > A岗职工', '工资表']
2025-07-31 18:07:28.191 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:6034 | 导航变化: 工资表 > 2025年 > 7月 > A岗职工
2025-07-31 18:07:28.191 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8517 | 🚫 [用户要求] 表格切换不显示加载条: None -> None
2025-07-31 18:07:28.191 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8533 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-07-31 18:07:28.191 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:5941 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-07-31 18:07:28.191 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:6734 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '7月', 'A岗职工'] -> salary_data_2025_07_a_grade_employees
2025-07-31 18:07:28.207 | INFO     | src.gui.widgets.pagination_widget:reset:620 | 分页状态已重置
2025-07-31 18:07:28.207 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_07_a_grade_employees 的缓存
2025-07-31 18:07:28.207 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:8985 | 已注册 4 个表格到表头管理器
2025-07-31 18:07:28.207 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1084 | 🔧 [P1-3] 开始智能表头重影检测，共 4 个表格
2025-07-31 18:07:28.207 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1157 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-07-31 18:07:28.207 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1158 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-07-31 18:07:28.207 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:6115 | 🆕 使用新架构加载数据: salary_data_2025_07_a_grade_employees（通过事件系统）
2025-07-31 18:07:28.207 | INFO     | src.services.table_data_service:load_table_data:347 | 加载表格数据: salary_data_2025_07_a_grade_employees, 页码: 1
2025-07-31 18:07:28.207 | INFO     | src.core.unified_data_request_manager:request_table_data:203 | 开始处理数据请求: salary_data_2025_07_a_grade_employees, 类型: initial_load
2025-07-31 18:07:28.207 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_a_grade_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-07-31 18:07:28.207 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_a_grade_employees" LIMIT 50 OFFSET 0
2025-07-31 18:07:28.222 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['34660024.0', '20222002.0', '14660141.0', '34660002.0', '34660010.0']
2025-07-31 18:07:28.222 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_a_grade_employees 获取第1页数据（含排序）: 50 行，总计62行
2025-07-31 18:07:28.222 | INFO     | src.core.unified_data_request_manager:request_table_data:252 | 数据请求处理完成: 26字段, 50行, 耗时15.7ms
2025-07-31 18:07:28.222 | INFO     | src.services.table_data_service:load_table_data:434 | [性能优化] 使用单例格式化管理器，避免重复初始化
2025-07-31 18:07:28.222 | INFO     | src.services.table_data_service:load_table_data:452 | [统一格式化] 数据格式化完成: salary_data_2025_07_a_grade_employees, 类型: active_employees
2025-07-31 18:07:28.222 | INFO     | src.services.table_data_service:load_table_data:489 | [修复数据发布] 数据加载成功，发布更新事件: salary_data_2025_07_a_grade_employees, 50行
2025-07-31 18:07:28.222 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3317 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_a_grade_employees
2025-07-31 18:07:28.238 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3342 | 数据内容: 50行 x 26列
2025-07-31 18:07:28.238 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3368 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_07_a_grade_employees, 50行
2025-07-31 18:07:28.238 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_a_grade_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:07:28.238 | INFO     | src.gui.prototype.prototype_main_window:set_data:728 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-31 18:07:28.238 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_a_grade_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:07:28.238 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=34660024.0, 薪资=N/A
2025-07-31 18:07:28.238 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20222002.0, 薪资=N/A
2025-07-31 18:07:28.238 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:4558 | 最大可见行数已更新: 50 -> 50
2025-07-31 18:07:28.238 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-31 18:07:28.254 | INFO     | src.modules.format_management.format_renderer:render_dataframe:143 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 22
2025-07-31 18:07:28.254 | WARNING  | src.modules.format_management.master_format_manager:_verify_formatting_completeness:694 | 🔧 [格式化验证] 以下字段格式化不完整: ['人员类别代码', '车补', '补发', '借支', '代扣代存养老保险', '月份']
2025-07-31 18:07:28.254 | WARNING  | src.modules.format_management.master_format_manager:format_table_data:226 | 🎯 [统一格式化] 格式化验证未通过，不标记: active_employees
2025-07-31 18:07:28.254 | INFO     | src.modules.format_management.master_format_manager:format_table_data:232 | 🎯 [统一格式化] 数据格式化完成: active_employees
2025-07-31 18:07:28.254 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2517 | 数据格式化完成: salary_data_2025_07_a_grade_employees, 类型: active_employees
2025-07-31 18:07:28.269 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-07-31 18:07:28.269 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 50行 x 22列
2025-07-31 18:07:28.285 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时15.7ms, 平均每行0.31ms
2025-07-31 18:07:28.285 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=15.7ms, 策略=small_dataset
2025-07-31 18:07:28.285 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-07-31 18:07:28.285 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2598 | [数据流追踪] 优化渲染完成: 50行 x 22列, 策略=small_dataset, 耗时=15.7ms, 性能评级=excellent
2025-07-31 18:07:28.285 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 34660024
2025-07-31 18:07:28.285 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 20222002
2025-07-31 18:07:28.285 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 14660141
2025-07-31 18:07:28.285 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 34660002
2025-07-31 18:07:28.285 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 34660010
2025-07-31 18:07:28.285 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-07-31 18:07:28.285 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[0]: 工号=34660024, 薪资=N/A
2025-07-31 18:07:28.300 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[1]: 工号=20222002, 薪资=N/A
2025-07-31 18:07:28.300 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[2]: 工号=14660141, 薪资=N/A
2025-07-31 18:07:28.300 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[3]: 工号=34660002, 薪资=N/A
2025-07-31 18:07:28.300 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[4]: 工号=34660010, 薪资=N/A
2025-07-31 18:07:28.300 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:472 | 可见行数据顺序: ['34660024', '20222002', '14660141', '34660002', '34660010']
2025-07-31 18:07:28.300 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 50 行, 22 列
2025-07-31 18:07:28.300 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:409 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_07_a_grade_employees
2025-07-31 18:07:28.300 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:440 | 🔧 [新架构] 为表格 salary_data_2025_07_a_grade_employees 重新加载 26 个字段映射
2025-07-31 18:07:28.300 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2678 | 表格数据设置完成: 50 行, 耗时: 62.2ms
2025-07-31 18:07:28.300 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7115 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-31 18:07:28.300 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7128 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-31 18:07:28.300 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1632 | 🆕 [列宽保存] 未找到表格 salary_data_2025_07_a_grade_employees 的列宽配置
2025-07-31 18:07:28.300 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 50
2025-07-31 18:07:28.313 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3400 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 22列
2025-07-31 18:07:28.325 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 62
2025-07-31 18:07:28.325 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3414 | 🔧 [分页修复] 数据更新事件设置总记录数: 62
2025-07-31 18:07:28.326 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3433 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=2, 总记录数=62
2025-07-31 18:07:28.327 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3439 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=False
2025-07-31 18:07:28.328 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3450 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-50
2025-07-31 18:07:28.329 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3721 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-07-31 18:07:28.329 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3722 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 62, 'start_record': 1, 'end_record': 50}
2025-07-31 18:07:28.330 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3723 | 🔍 [表格调试] 当前表格行数: 50
2025-07-31 18:07:28.331 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3724 | 🔍 [表格调试] 当前表格列数: 22
2025-07-31 18:07:28.332 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3726 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-07-31 18:07:28.332 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3752 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-07-31 18:07:28.338 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 22 个, 行号起始 1, 共 50 行
2025-07-31 18:07:28.340 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:3831 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-07-31 18:07:28.341 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3452 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-07-31 18:07:28.390 | INFO     | src.gui.table_header_manager:cleanup_callback:131 | 🔧 [P1-1] 表格 table_3_2337132022816 已自动清理（弱引用回调）
2025-07-31 18:07:28.390 | INFO     | src.gui.table_header_manager:cleanup_callback:131 | 🔧 [P1-1] 表格 table_2_2337132023392 已自动清理（弱引用回调）
2025-07-31 18:07:28.407 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-07-31 18:07:28.407 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:8793 | ✅ [新架构] 组件状态一致性验证通过
2025-07-31 18:07:28.409 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:8756 | 🆕 [新架构] 导航迁移完成
2025-07-31 18:07:28.410 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3466 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-07-31 18:07:28.410 | INFO     | src.services.table_data_service:load_table_data:510 | [修复数据发布] 数据更新事件已发布
2025-07-31 18:07:28.411 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:6120 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-07-31 18:07:28.412 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:896 | 导航选择: 工资表 > 2025年 > 7月 > A岗职工
2025-07-31 18:07:40.493 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 7月 > A岗职工', '工资表 > 2025年 > 7月', '工资表 > 2025年 > 7月 > 全部在职人员', '工资表']
2025-07-31 18:07:40.493 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:6034 | 导航变化: 工资表 > 2025年 > 7月 > 全部在职人员
2025-07-31 18:07:40.493 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8517 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_07_a_grade_employees -> None
2025-07-31 18:07:40.493 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8533 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-07-31 18:07:40.508 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:5941 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-07-31 18:07:40.508 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:6734 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '7月', '全部在职人员'] -> salary_data_2025_07_active_employees
2025-07-31 18:07:40.508 | INFO     | src.gui.widgets.pagination_widget:reset:620 | 分页状态已重置
2025-07-31 18:07:40.508 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_07_active_employees 的缓存
2025-07-31 18:07:40.508 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:8985 | 已注册 2 个表格到表头管理器
2025-07-31 18:07:40.508 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1084 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-07-31 18:07:40.508 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1157 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-07-31 18:07:40.508 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1158 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-07-31 18:07:40.508 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:6115 | 🆕 使用新架构加载数据: salary_data_2025_07_active_employees（通过事件系统）
2025-07-31 18:07:40.508 | INFO     | src.services.table_data_service:load_table_data:347 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-31 18:07:40.508 | INFO     | src.core.unified_data_request_manager:request_table_data:203 | 开始处理数据请求: salary_data_2025_07_active_employees, 类型: initial_load
2025-07-31 18:07:40.508 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_active_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-07-31 18:07:40.508 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_active_employees" LIMIT 50 OFFSET 0
2025-07-31 18:07:40.508 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19990089.0', '20161565.0', '20191782.0', '20151515.0', '20181640.0']
2025-07-31 18:07:40.524 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_active_employees 获取第1页数据（含排序）: 50 行，总计1396行
2025-07-31 18:07:40.524 | INFO     | src.core.unified_data_request_manager:request_table_data:252 | 数据请求处理完成: 28字段, 50行, 耗时15.7ms
2025-07-31 18:07:40.524 | INFO     | src.services.table_data_service:load_table_data:434 | [性能优化] 使用单例格式化管理器，避免重复初始化
2025-07-31 18:07:40.524 | INFO     | src.services.table_data_service:load_table_data:452 | [统一格式化] 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-31 18:07:40.524 | INFO     | src.services.table_data_service:load_table_data:489 | [修复数据发布] 数据加载成功，发布更新事件: salary_data_2025_07_active_employees, 50行
2025-07-31 18:07:40.524 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3317 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-31 18:07:40.540 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3342 | 数据内容: 50行 x 28列
2025-07-31 18:07:40.540 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3368 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_07_active_employees, 50行
2025-07-31 18:07:40.540 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:07:40.540 | INFO     | src.gui.prototype.prototype_main_window:set_data:728 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-31 18:07:40.540 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:07:40.540 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089.0, 薪资=2375.0
2025-07-31 18:07:40.540 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565.0, 薪资=1696.0
2025-07-31 18:07:40.540 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:4558 | 最大可见行数已更新: 50 -> 50
2025-07-31 18:07:40.540 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-31 18:07:40.555 | INFO     | src.modules.format_management.format_renderer:render_dataframe:143 | 🎨 [格式渲染] DataFrame格式化完成: active_employees, 行数: 50, 列数: 24
2025-07-31 18:07:40.555 | WARNING  | src.modules.format_management.master_format_manager:_verify_formatting_completeness:694 | 🔧 [格式化验证] 以下字段格式化不完整: ['人员类别代码', '车补', '补发', '借支', '代扣代存养老保险', '月份']
2025-07-31 18:07:40.555 | WARNING  | src.modules.format_management.master_format_manager:format_table_data:226 | 🎯 [统一格式化] 格式化验证未通过，不标记: active_employees
2025-07-31 18:07:40.555 | INFO     | src.modules.format_management.master_format_manager:format_table_data:232 | 🎯 [统一格式化] 数据格式化完成: active_employees
2025-07-31 18:07:40.555 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2517 | 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-31 18:07:40.571 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-07-31 18:07:40.586 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-07-31 18:07:40.586 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时0.0ms, 平均每行0.00ms
2025-07-31 18:07:40.586 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=0.0ms, 策略=small_dataset
2025-07-31 18:07:40.586 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-07-31 18:07:40.586 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2598 | [数据流追踪] 优化渲染完成: 50行 x 24列, 策略=small_dataset, 耗时=0.0ms, 性能评级=excellent
2025-07-31 18:07:40.586 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 19990089
2025-07-31 18:07:40.586 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 20161565
2025-07-31 18:07:40.602 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 20191782
2025-07-31 18:07:40.602 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 20151515
2025-07-31 18:07:40.602 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 20181640
2025-07-31 18:07:40.602 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-07-31 18:07:40.602 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375
2025-07-31 18:07:40.602 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696
2025-07-31 18:07:40.602 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427
2025-07-31 18:07:40.602 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175
2025-07-31 18:07:40.602 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582
2025-07-31 18:07:40.602 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:472 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-31 18:07:40.602 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 50 行, 24 列
2025-07-31 18:07:40.602 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:409 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_07_active_employees
2025-07-31 18:07:40.617 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:440 | 🔧 [新架构] 为表格 salary_data_2025_07_active_employees 重新加载 28 个字段映射
2025-07-31 18:07:40.617 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2678 | 表格数据设置完成: 50 行, 耗时: 62.2ms
2025-07-31 18:07:40.617 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7115 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-31 18:07:40.617 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7128 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-31 18:07:40.617 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1657 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-31 18:07:40.617 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 50
2025-07-31 18:07:40.629 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3400 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-07-31 18:07:40.630 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 1396
2025-07-31 18:07:40.630 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3414 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-07-31 18:07:40.631 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3433 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=28, 总记录数=1396
2025-07-31 18:07:40.632 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3439 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=False
2025-07-31 18:07:40.639 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3450 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-50
2025-07-31 18:07:40.640 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3721 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-07-31 18:07:40.641 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3722 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 1396, 'start_record': 1, 'end_record': 50}
2025-07-31 18:07:40.641 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3723 | 🔍 [表格调试] 当前表格行数: 50
2025-07-31 18:07:40.643 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3724 | 🔍 [表格调试] 当前表格列数: 24
2025-07-31 18:07:40.643 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3726 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-07-31 18:07:40.644 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3752 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-07-31 18:07:40.645 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-07-31 18:07:40.646 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:3831 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-07-31 18:07:40.647 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3452 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-07-31 18:07:40.698 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-07-31 18:07:40.699 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:8793 | ✅ [新架构] 组件状态一致性验证通过
2025-07-31 18:07:40.702 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:8756 | 🆕 [新架构] 导航迁移完成
2025-07-31 18:07:40.704 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3466 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-07-31 18:07:40.704 | INFO     | src.services.table_data_service:load_table_data:510 | [修复数据发布] 数据更新事件已发布
2025-07-31 18:07:40.705 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:6120 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-07-31 18:07:40.706 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:896 | 导航选择: 工资表 > 2025年 > 7月 > 全部在职人员
2025-07-31 18:07:40.918 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1597 | 🔧 [列宽保存修复] 准备保存列宽到文件: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-07-31 18:07:40.918 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1598 | 🔧 [列宽保存修复] 表名: salary_data_2025_07_active_employees, 列数: 24
2025-07-31 18:07:40.918 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1603 | 🔧 [列宽保存修复] 列宽设置保存成功！文件大小: 1901 字节
2025-07-31 18:07:46.728 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 7月 > A岗职工', '工资表 > 2025年 > 7月', '工资表 > 2025年 > 7月 > 退休人员', '工资表 > 2025年 > 7月 > 全部在职人员']
2025-07-31 18:07:46.744 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:6034 | 导航变化: 工资表 > 2025年 > 7月 > 退休人员
2025-07-31 18:07:46.744 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8517 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_07_active_employees -> None
2025-07-31 18:07:46.744 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8533 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-07-31 18:07:46.744 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:5941 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-07-31 18:07:46.744 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:6734 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '7月', '退休人员'] -> salary_data_2025_07_pension_employees
2025-07-31 18:07:46.744 | INFO     | src.gui.widgets.pagination_widget:reset:620 | 分页状态已重置
2025-07-31 18:07:46.744 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_07_pension_employees 的缓存
2025-07-31 18:07:46.744 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:8985 | 已注册 2 个表格到表头管理器
2025-07-31 18:07:46.744 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1084 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-07-31 18:07:46.744 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1157 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-07-31 18:07:46.744 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1158 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-07-31 18:07:46.744 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:6115 | 🆕 使用新架构加载数据: salary_data_2025_07_pension_employees（通过事件系统）
2025-07-31 18:07:46.744 | INFO     | src.services.table_data_service:load_table_data:347 | 加载表格数据: salary_data_2025_07_pension_employees, 页码: 1
2025-07-31 18:07:46.744 | INFO     | src.core.unified_data_request_manager:request_table_data:203 | 开始处理数据请求: salary_data_2025_07_pension_employees, 类型: initial_load
2025-07-31 18:07:46.744 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_pension_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-07-31 18:07:46.760 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_pension_employees" LIMIT 50 OFFSET 0
2025-07-31 18:07:46.760 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19709165.0', '19981259.0', '19721294.0', '19841258.0', '19499098.0']
2025-07-31 18:07:46.760 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_pension_employees 获取第1页数据（含排序）: 13 行，总计13行
2025-07-31 18:07:46.760 | INFO     | src.core.unified_data_request_manager:request_table_data:252 | 数据请求处理完成: 32字段, 13行, 耗时16.6ms
2025-07-31 18:07:46.760 | INFO     | src.services.table_data_service:load_table_data:434 | [性能优化] 使用单例格式化管理器，避免重复初始化
2025-07-31 18:07:46.760 | INFO     | src.services.table_data_service:load_table_data:452 | [统一格式化] 数据格式化完成: salary_data_2025_07_pension_employees, 类型: retired_employees
2025-07-31 18:07:46.776 | INFO     | src.services.table_data_service:load_table_data:489 | [修复数据发布] 数据加载成功，发布更新事件: salary_data_2025_07_pension_employees, 13行
2025-07-31 18:07:46.776 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3317 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_pension_employees
2025-07-31 18:07:46.776 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3342 | 数据内容: 13行 x 32列
2025-07-31 18:07:46.776 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3368 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_07_pension_employees, 13行
2025-07-31 18:07:46.776 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_pension_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:07:46.776 | INFO     | src.gui.prototype.prototype_main_window:set_data:728 | 通过公共接口设置新的表格数据(DataFrame): 13 行
2025-07-31 18:07:46.776 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_pension_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:07:46.776 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=未知0, 薪资=N/A
2025-07-31 18:07:46.776 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=未知1, 薪资=N/A
2025-07-31 18:07:46.791 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:4558 | 最大可见行数已更新: 50 -> 13
2025-07-31 18:07:46.791 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4611 | 根据数据量(13)自动调整最大可见行数为: 13
2025-07-31 18:07:46.806 | INFO     | src.modules.format_management.format_renderer:render_dataframe:143 | 🎨 [格式渲染] DataFrame格式化完成: retired_employees, 行数: 13, 列数: 28
2025-07-31 18:07:46.806 | WARNING  | src.modules.format_management.master_format_manager:_verify_formatting_completeness:694 | 🔧 [格式化验证] 以下字段格式化不完整: ['人员类别代码', '补发', '借支', '月份']
2025-07-31 18:07:46.806 | WARNING  | src.modules.format_management.master_format_manager:format_table_data:226 | 🎯 [统一格式化] 格式化验证未通过，不标记: retired_employees
2025-07-31 18:07:46.806 | INFO     | src.modules.format_management.master_format_manager:format_table_data:232 | 🎯 [统一格式化] 数据格式化完成: retired_employees
2025-07-31 18:07:46.806 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2517 | 数据格式化完成: salary_data_2025_07_pension_employees, 类型: retired_employees
2025-07-31 18:07:46.823 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 13行数据
2025-07-31 18:07:46.823 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 13行 x 28列
2025-07-31 18:07:46.823 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时0.0ms, 平均每行0.00ms
2025-07-31 18:07:46.823 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=13, 渲染时间=0.0ms, 策略=small_dataset
2025-07-31 18:07:46.823 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-07-31 18:07:46.823 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2598 | [数据流追踪] 优化渲染完成: 13行 x 28列, 策略=small_dataset, 耗时=0.0ms, 性能评级=excellent
2025-07-31 18:07:46.823 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 未知0
2025-07-31 18:07:46.823 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 未知1
2025-07-31 18:07:46.823 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 未知2
2025-07-31 18:07:46.823 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 未知3
2025-07-31 18:07:46.823 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 未知4
2025-07-31 18:07:46.823 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 13
2025-07-31 18:07:46.823 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:474 | 🚨 [UI数据修复] 无法找到工号字段，first_row存在: True
2025-07-31 18:07:46.839 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:476 | 🚨 [UI数据修复] main_data字段: ['人员代码', '姓名', '部门名称', '人员类别代码', '基本退休费', '津贴', '结余津贴', '离退休生活补贴', '护理费', '物业补贴', '住房补贴', '增资预付', '2016待遇调整', '2017待遇调整', '2018待遇调整', '2019待遇调整', '2020待遇调整', '2021待遇调整', '2022待遇调整', '2023待遇调整', '补发', '借支', '应发工资', '公积', '保险扣款', '备注', '月份', '年份']
2025-07-31 18:07:46.839 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 13 行, 28 列
2025-07-31 18:07:46.839 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:409 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_07_pension_employees
2025-07-31 18:07:46.839 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:440 | 🔧 [新架构] 为表格 salary_data_2025_07_pension_employees 重新加载 32 个字段映射
2025-07-31 18:07:46.839 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2678 | 表格数据设置完成: 13 行, 耗时: 63.2ms
2025-07-31 18:07:46.839 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7115 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-31 18:07:46.839 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7128 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-31 18:07:46.853 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1632 | 🆕 [列宽保存] 未找到表格 salary_data_2025_07_pension_employees 的列宽配置
2025-07-31 18:07:46.853 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 13
2025-07-31 18:07:46.855 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3400 | 🔧 [统一数据设置] 数据已成功设置到UI: 13行, 28列
2025-07-31 18:07:46.855 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3433 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=1, 总记录数=13
2025-07-31 18:07:46.856 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3439 | 🔧 [分页修复] 按钮可用性: 下一页=False, 上一页=False
2025-07-31 18:07:46.856 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3450 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-13
2025-07-31 18:07:46.864 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3721 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-07-31 18:07:46.864 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3722 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 13, 'start_record': 1, 'end_record': 13}
2025-07-31 18:07:46.866 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3723 | 🔍 [表格调试] 当前表格行数: 13
2025-07-31 18:07:46.867 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3724 | 🔍 [表格调试] 当前表格列数: 28
2025-07-31 18:07:46.867 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3726 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-13
2025-07-31 18:07:46.868 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3752 | 🔧 [分页行号修复] 表格状态: 当前行数=13, 期望行数=13
2025-07-31 18:07:46.869 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 28 个, 行号起始 1, 共 13 行
2025-07-31 18:07:46.870 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:3831 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共13行
2025-07-31 18:07:46.871 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3452 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-07-31 18:07:46.919 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-07-31 18:07:46.920 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:8793 | ✅ [新架构] 组件状态一致性验证通过
2025-07-31 18:07:46.923 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:8756 | 🆕 [新架构] 导航迁移完成
2025-07-31 18:07:46.923 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3466 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-07-31 18:07:46.924 | INFO     | src.services.table_data_service:load_table_data:510 | [修复数据发布] 数据更新事件已发布
2025-07-31 18:07:46.925 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:6120 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-07-31 18:07:46.925 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:896 | 导航选择: 工资表 > 2025年 > 7月 > 退休人员
2025-07-31 18:07:48.516 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 7月 > A岗职工', '工资表 > 2025年 > 7月', '工资表 > 2025年 > 7月 > 离休人员', '工资表 > 2025年 > 7月 > 退休人员']
2025-07-31 18:07:48.516 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:6034 | 导航变化: 工资表 > 2025年 > 7月 > 离休人员
2025-07-31 18:07:48.516 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8517 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_07_pension_employees -> None
2025-07-31 18:07:48.516 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8533 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-07-31 18:07:48.516 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:5941 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-07-31 18:07:48.516 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:6734 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '7月', '离休人员'] -> salary_data_2025_07_retired_employees
2025-07-31 18:07:48.516 | INFO     | src.gui.widgets.pagination_widget:reset:620 | 分页状态已重置
2025-07-31 18:07:48.516 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_07_retired_employees 的缓存
2025-07-31 18:07:48.516 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:8985 | 已注册 2 个表格到表头管理器
2025-07-31 18:07:48.516 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1084 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-07-31 18:07:48.516 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1157 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-07-31 18:07:48.516 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1158 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-07-31 18:07:48.516 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:6115 | 🆕 使用新架构加载数据: salary_data_2025_07_retired_employees（通过事件系统）
2025-07-31 18:07:48.532 | INFO     | src.services.table_data_service:load_table_data:347 | 加载表格数据: salary_data_2025_07_retired_employees, 页码: 1
2025-07-31 18:07:48.532 | INFO     | src.core.unified_data_request_manager:request_table_data:203 | 开始处理数据请求: salary_data_2025_07_retired_employees, 类型: initial_load
2025-07-31 18:07:48.532 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_retired_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-07-31 18:07:48.532 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_retired_employees" LIMIT 50 OFFSET 0
2025-07-31 18:07:48.532 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19289006.0', '19339009.0']
2025-07-31 18:07:48.532 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_retired_employees 获取第1页数据（含排序）: 2 行，总计2行
2025-07-31 18:07:48.532 | INFO     | src.core.unified_data_request_manager:request_table_data:252 | 数据请求处理完成: 21字段, 2行, 耗时0.0ms
2025-07-31 18:07:48.532 | INFO     | src.services.table_data_service:load_table_data:434 | [性能优化] 使用单例格式化管理器，避免重复初始化
2025-07-31 18:07:48.548 | INFO     | src.services.table_data_service:load_table_data:452 | [统一格式化] 数据格式化完成: salary_data_2025_07_retired_employees, 类型: retired_employees
2025-07-31 18:07:48.548 | INFO     | src.services.table_data_service:load_table_data:489 | [修复数据发布] 数据加载成功，发布更新事件: salary_data_2025_07_retired_employees, 2行
2025-07-31 18:07:48.548 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3317 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_retired_employees
2025-07-31 18:07:48.548 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3342 | 数据内容: 2行 x 21列
2025-07-31 18:07:48.548 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3368 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_07_retired_employees, 2行
2025-07-31 18:07:48.548 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_retired_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:07:48.548 | INFO     | src.gui.prototype.prototype_main_window:set_data:728 | 通过公共接口设置新的表格数据(DataFrame): 2 行
2025-07-31 18:07:48.548 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_retired_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:07:48.548 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=未知0, 薪资=N/A
2025-07-31 18:07:48.548 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=未知1, 薪资=N/A
2025-07-31 18:07:48.565 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:4549 | 最大可见行数过小可能影响性能，建议至少设置为10，当前值: 2
2025-07-31 18:07:48.565 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:4558 | 最大可见行数已更新: 13 -> 2
2025-07-31 18:07:48.565 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4611 | 根据数据量(2)自动调整最大可见行数为: 2
2025-07-31 18:07:48.579 | INFO     | src.modules.format_management.format_renderer:render_dataframe:143 | 🎨 [格式渲染] DataFrame格式化完成: retired_employees, 行数: 2, 列数: 17
2025-07-31 18:07:48.579 | WARNING  | src.modules.format_management.master_format_manager:_verify_formatting_completeness:694 | 🔧 [格式化验证] 以下字段格式化不完整: ['补发', '借支', '月份']
2025-07-31 18:07:48.579 | WARNING  | src.modules.format_management.master_format_manager:format_table_data:226 | 🎯 [统一格式化] 格式化验证未通过，不标记: retired_employees
2025-07-31 18:07:48.579 | INFO     | src.modules.format_management.master_format_manager:format_table_data:232 | 🎯 [统一格式化] 数据格式化完成: retired_employees
2025-07-31 18:07:48.579 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2517 | 数据格式化完成: salary_data_2025_07_retired_employees, 类型: retired_employees
2025-07-31 18:07:48.579 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 2行数据
2025-07-31 18:07:48.579 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 2行 x 17列
2025-07-31 18:07:48.579 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时0.0ms, 平均每行0.00ms
2025-07-31 18:07:48.579 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=2, 渲染时间=0.0ms, 策略=small_dataset
2025-07-31 18:07:48.579 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-07-31 18:07:48.579 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2598 | [数据流追踪] 优化渲染完成: 2行 x 17列, 策略=small_dataset, 耗时=0.0ms, 性能评级=excellent
2025-07-31 18:07:48.579 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 未知0
2025-07-31 18:07:48.579 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 未知1
2025-07-31 18:07:48.579 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 2
2025-07-31 18:07:48.579 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:474 | 🚨 [UI数据修复] 无法找到工号字段，first_row存在: True
2025-07-31 18:07:48.595 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:476 | 🚨 [UI数据修复] main_data字段: ['人员代码', '姓名', '部门名称', '基本离休费', '结余津贴', '生活补贴', '住房补贴', '物业补贴', '离休补贴', '护理费', '增发一次性生活补贴', '补发', '合计', '借支', '备注', '月份', '年份']
2025-07-31 18:07:48.595 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 2 行, 17 列
2025-07-31 18:07:48.595 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:409 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_07_retired_employees
2025-07-31 18:07:48.595 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:440 | 🔧 [新架构] 为表格 salary_data_2025_07_retired_employees 重新加载 21 个字段映射
2025-07-31 18:07:48.595 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2678 | 表格数据设置完成: 2 行, 耗时: 47.0ms
2025-07-31 18:07:48.595 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7115 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-31 18:07:48.595 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7128 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-31 18:07:48.595 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1632 | 🆕 [列宽保存] 未找到表格 salary_data_2025_07_retired_employees 的列宽配置
2025-07-31 18:07:48.595 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 2
2025-07-31 18:07:48.613 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3400 | 🔧 [统一数据设置] 数据已成功设置到UI: 2行, 17列
2025-07-31 18:07:48.613 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3433 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=1, 总记录数=2
2025-07-31 18:07:48.614 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3439 | 🔧 [分页修复] 按钮可用性: 下一页=False, 上一页=False
2025-07-31 18:07:48.621 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3450 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-2
2025-07-31 18:07:48.622 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3721 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-07-31 18:07:48.622 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3722 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 2, 'start_record': 1, 'end_record': 2}
2025-07-31 18:07:48.623 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3723 | 🔍 [表格调试] 当前表格行数: 2
2025-07-31 18:07:48.624 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3724 | 🔍 [表格调试] 当前表格列数: 17
2025-07-31 18:07:48.625 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3726 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-2
2025-07-31 18:07:48.626 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3752 | 🔧 [分页行号修复] 表格状态: 当前行数=2, 期望行数=2
2025-07-31 18:07:48.627 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 17 个, 行号起始 1, 共 2 行
2025-07-31 18:07:48.627 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:3831 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共2行
2025-07-31 18:07:48.628 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3452 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-07-31 18:07:48.672 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-07-31 18:07:48.672 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:8793 | ✅ [新架构] 组件状态一致性验证通过
2025-07-31 18:07:48.675 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:8756 | 🆕 [新架构] 导航迁移完成
2025-07-31 18:07:48.676 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3466 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-07-31 18:07:48.677 | INFO     | src.services.table_data_service:load_table_data:510 | [修复数据发布] 数据更新事件已发布
2025-07-31 18:07:48.677 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:6120 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-07-31 18:07:48.678 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:896 | 导航选择: 工资表 > 2025年 > 7月 > 离休人员
2025-07-31 18:08:15.675 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 7月 > 退休人员', '工资表 > 2025年 > 7月 > A岗职工', '工资表 > 2025年 > 7月', '工资表 > 2025年 > 7月 > 离休人员']
2025-07-31 18:08:15.675 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:6034 | 导航变化: 工资表 > 2025年 > 7月 > 退休人员
2025-07-31 18:08:15.690 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8517 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_07_retired_employees -> None
2025-07-31 18:08:15.690 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8533 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-07-31 18:08:15.690 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:5941 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-07-31 18:08:15.690 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:6734 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '7月', '退休人员'] -> salary_data_2025_07_pension_employees
2025-07-31 18:08:15.690 | INFO     | src.gui.widgets.pagination_widget:reset:620 | 分页状态已重置
2025-07-31 18:08:15.690 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_07_pension_employees 的缓存
2025-07-31 18:08:15.690 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:8985 | 已注册 2 个表格到表头管理器
2025-07-31 18:08:15.690 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1084 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-07-31 18:08:15.690 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1157 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-07-31 18:08:15.690 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1158 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-07-31 18:08:15.690 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:6115 | 🆕 使用新架构加载数据: salary_data_2025_07_pension_employees（通过事件系统）
2025-07-31 18:08:15.690 | INFO     | src.services.table_data_service:load_table_data:347 | 加载表格数据: salary_data_2025_07_pension_employees, 页码: 1
2025-07-31 18:08:15.690 | INFO     | src.services.table_data_service:load_table_data:370 | [缓存命中] 使用缓存数据: salary_data_2025_07_pension_employees 第1页
2025-07-31 18:08:15.690 | INFO     | src.services.table_data_service:load_table_data:376 | [根本修复] 缓存命中，发布数据更新事件: salary_data_2025_07_pension_employees, 13行
2025-07-31 18:08:15.690 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3317 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_pension_employees
2025-07-31 18:08:15.690 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3342 | 数据内容: 13行 x 32列
2025-07-31 18:08:15.690 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3368 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_07_pension_employees, 13行
2025-07-31 18:08:15.706 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_pension_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:08:15.706 | INFO     | src.gui.prototype.prototype_main_window:set_data:728 | 通过公共接口设置新的表格数据(DataFrame): 13 行
2025-07-31 18:08:15.706 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_pension_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:08:15.721 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=未知0, 薪资=N/A
2025-07-31 18:08:15.721 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=未知1, 薪资=N/A
2025-07-31 18:08:15.721 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:4558 | 最大可见行数已更新: 2 -> 13
2025-07-31 18:08:15.721 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4611 | 根据数据量(13)自动调整最大可见行数为: 13
2025-07-31 18:08:15.721 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2517 | 数据格式化完成: salary_data_2025_07_pension_employees, 类型: retired_employees
2025-07-31 18:08:15.721 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 13行数据
2025-07-31 18:08:15.721 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 13行 x 28列
2025-07-31 18:08:15.737 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时15.7ms, 平均每行1.21ms
2025-07-31 18:08:15.737 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=13, 渲染时间=15.7ms, 策略=small_dataset
2025-07-31 18:08:15.737 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-07-31 18:08:15.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2598 | [数据流追踪] 优化渲染完成: 13行 x 28列, 策略=small_dataset, 耗时=15.7ms, 性能评级=excellent
2025-07-31 18:08:15.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 未知0
2025-07-31 18:08:15.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 未知1
2025-07-31 18:08:15.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 未知2
2025-07-31 18:08:15.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 未知3
2025-07-31 18:08:15.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 未知4
2025-07-31 18:08:15.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 13
2025-07-31 18:08:15.737 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:474 | 🚨 [UI数据修复] 无法找到工号字段，first_row存在: True
2025-07-31 18:08:15.737 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:476 | 🚨 [UI数据修复] main_data字段: ['人员代码', '姓名', '部门名称', '人员类别代码', '基本退休费', '津贴', '结余津贴', '离退休生活补贴', '护理费', '物业补贴', '住房补贴', '增资预付', '2016待遇调整', '2017待遇调整', '2018待遇调整', '2019待遇调整', '2020待遇调整', '2021待遇调整', '2022待遇调整', '2023待遇调整', '补发', '借支', '应发工资', '公积', '保险扣款', '备注', '月份', '年份']
2025-07-31 18:08:15.753 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 13 行, 28 列
2025-07-31 18:08:15.753 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:409 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_07_pension_employees
2025-07-31 18:08:15.753 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:440 | 🔧 [新架构] 为表格 salary_data_2025_07_pension_employees 重新加载 32 个字段映射
2025-07-31 18:08:15.753 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2678 | 表格数据设置完成: 13 行, 耗时: 31.3ms
2025-07-31 18:08:15.753 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7115 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-31 18:08:15.753 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7128 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-31 18:08:15.753 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1632 | 🆕 [列宽保存] 未找到表格 salary_data_2025_07_pension_employees 的列宽配置
2025-07-31 18:08:15.753 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 13
2025-07-31 18:08:15.767 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3400 | 🔧 [统一数据设置] 数据已成功设置到UI: 13行, 28列
2025-07-31 18:08:15.767 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3433 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=1, 总记录数=13
2025-07-31 18:08:15.768 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3439 | 🔧 [分页修复] 按钮可用性: 下一页=False, 上一页=False
2025-07-31 18:08:15.775 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3450 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-13
2025-07-31 18:08:15.776 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3721 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-07-31 18:08:15.776 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3722 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 13, 'start_record': 1, 'end_record': 13}
2025-07-31 18:08:15.777 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3723 | 🔍 [表格调试] 当前表格行数: 13
2025-07-31 18:08:15.778 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3724 | 🔍 [表格调试] 当前表格列数: 28
2025-07-31 18:08:15.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3726 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-13
2025-07-31 18:08:15.779 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3752 | 🔧 [分页行号修复] 表格状态: 当前行数=13, 期望行数=13
2025-07-31 18:08:15.781 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 28 个, 行号起始 1, 共 13 行
2025-07-31 18:08:15.782 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:3831 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共13行
2025-07-31 18:08:15.782 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3452 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-07-31 18:08:15.824 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-07-31 18:08:15.825 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:8793 | ✅ [新架构] 组件状态一致性验证通过
2025-07-31 18:08:15.828 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:8756 | 🆕 [新架构] 导航迁移完成
2025-07-31 18:08:15.828 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3466 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-07-31 18:08:15.829 | INFO     | src.services.table_data_service:load_table_data:398 | [根本修复] 缓存数据更新事件已发布，UI应该能正常更新
2025-07-31 18:08:15.830 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:6120 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-07-31 18:08:15.831 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:896 | 导航选择: 工资表 > 2025年 > 7月 > 退休人员
2025-07-31 18:09:25.662 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:606 | 刷新数据功能被触发，发出 refresh_requested 信号
2025-07-31 18:09:25.662 | INFO     | src.gui.prototype.prototype_main_window:_on_refresh_data:6999 | 🔧 [P1-3] 全局状态刷新被触发，开始综合性系统刷新。
2025-07-31 18:09:25.662 | INFO     | src.gui.prototype.prototype_main_window:_execute_global_state_refresh:7013 | 🔧 [P1-3] 开始执行全局状态刷新流程
2025-07-31 18:09:25.662 | INFO     | src.gui.prototype.prototype_main_window:_refresh_core_components:7079 | 🔧 [P1-3] 开始刷新核心组件状态
2025-07-31 18:09:25.662 | INFO     | src.gui.table_header_manager:force_comprehensive_cleanup:842 | 🔧 [P1-1] 开始强制全面清理 2 个表格
2025-07-31 18:09:25.662 | WARNING  | src.gui.table_header_manager:force_comprehensive_cleanup:851 | 表格 table_0_2336944883904 对象已无效，跳过强制清理
2025-07-31 18:09:25.678 | INFO     | src.gui.table_header_manager:_remove_deleted_table_reference:500 | 🔧 [P0-1] 已移除已删除表格的引用: table_0_2336944883904
2025-07-31 18:09:25.678 | WARNING  | src.gui.table_header_manager:force_comprehensive_cleanup:851 | 表格 table_1_2336930109568 对象已无效，跳过强制清理
2025-07-31 18:09:25.678 | INFO     | src.gui.table_header_manager:_remove_deleted_table_reference:500 | 🔧 [P0-1] 已移除已删除表格的引用: table_1_2336930109568
2025-07-31 18:09:25.678 | INFO     | src.gui.table_header_manager:force_comprehensive_cleanup:901 | 🔧 [P1-1] 强制全面清理完成，成功率: 0.0% (0/2)
2025-07-31 18:09:25.678 | INFO     | src.gui.prototype.prototype_main_window:_refresh_core_components:7087 | 🔧 [P1-3] 表头重影清理完成: {'total_tables': 2, 'cleaned_tables': [], 'failed_tables': [], 'total_shadows_found': 0, 'total_shadows_fixed': 0}
2025-07-31 18:09:25.678 | INFO     | src.gui.prototype.prototype_main_window:_refresh_core_components:7110 | 🔧 [P1-3] 核心组件状态刷新完成
2025-07-31 18:09:25.678 | INFO     | src.gui.prototype.prototype_main_window:_refresh_data_layer:7124 | 🔧 [P1-3] 开始刷新数据层
2025-07-31 18:09:25.678 | INFO     | src.services.table_data_service:load_table_data:347 | 加载表格数据: salary_data_2025_07_pension_employees, 页码: 1
2025-07-31 18:09:25.678 | INFO     | src.core.unified_data_request_manager:request_table_data:203 | 开始处理数据请求: salary_data_2025_07_pension_employees, 类型: initial_load
2025-07-31 18:09:25.678 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_pension_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-07-31 18:09:25.678 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_pension_employees" LIMIT 50 OFFSET 0
2025-07-31 18:09:25.678 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19709165.0', '19981259.0', '19721294.0', '19841258.0', '19499098.0']
2025-07-31 18:09:25.678 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_pension_employees 获取第1页数据（含排序）: 13 行，总计13行
2025-07-31 18:09:25.678 | INFO     | src.core.unified_data_request_manager:request_table_data:252 | 数据请求处理完成: 32字段, 13行, 耗时0.0ms
2025-07-31 18:09:25.678 | INFO     | src.services.table_data_service:load_table_data:434 | [性能优化] 使用单例格式化管理器，避免重复初始化
2025-07-31 18:09:25.678 | INFO     | src.services.table_data_service:load_table_data:452 | [统一格式化] 数据格式化完成: salary_data_2025_07_pension_employees, 类型: retired_employees
2025-07-31 18:09:25.693 | INFO     | src.services.table_data_service:load_table_data:489 | [修复数据发布] 数据加载成功，发布更新事件: salary_data_2025_07_pension_employees, 13行
2025-07-31 18:09:25.710 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3317 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_pension_employees
2025-07-31 18:09:25.710 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3342 | 数据内容: 13行 x 32列
2025-07-31 18:09:25.710 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3368 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_07_pension_employees, 13行
2025-07-31 18:09:25.710 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_pension_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:09:25.710 | INFO     | src.gui.prototype.prototype_main_window:set_data:728 | 通过公共接口设置新的表格数据(DataFrame): 13 行
2025-07-31 18:09:25.725 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_pension_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:09:25.725 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=未知0, 薪资=N/A
2025-07-31 18:09:25.725 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=未知1, 薪资=N/A
2025-07-31 18:09:25.725 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:4558 | 最大可见行数已更新: 13 -> 13
2025-07-31 18:09:25.725 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4611 | 根据数据量(13)自动调整最大可见行数为: 13
2025-07-31 18:09:25.725 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2517 | 数据格式化完成: salary_data_2025_07_pension_employees, 类型: retired_employees
2025-07-31 18:09:25.740 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 13行数据
2025-07-31 18:09:25.740 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 13行 x 28列
2025-07-31 18:09:25.740 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时0.0ms, 平均每行0.00ms
2025-07-31 18:09:25.740 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=13, 渲染时间=0.0ms, 策略=small_dataset
2025-07-31 18:09:25.740 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-07-31 18:09:25.740 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2598 | [数据流追踪] 优化渲染完成: 13行 x 28列, 策略=small_dataset, 耗时=0.0ms, 性能评级=excellent
2025-07-31 18:09:25.740 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 未知0
2025-07-31 18:09:25.740 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 未知1
2025-07-31 18:09:25.740 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 未知2
2025-07-31 18:09:25.740 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 未知3
2025-07-31 18:09:25.740 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 未知4
2025-07-31 18:09:25.740 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 13
2025-07-31 18:09:25.740 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:474 | 🚨 [UI数据修复] 无法找到工号字段，first_row存在: True
2025-07-31 18:09:25.759 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:476 | 🚨 [UI数据修复] main_data字段: ['人员代码', '姓名', '部门名称', '人员类别代码', '基本退休费', '津贴', '结余津贴', '离退休生活补贴', '护理费', '物业补贴', '住房补贴', '增资预付', '2016待遇调整', '2017待遇调整', '2018待遇调整', '2019待遇调整', '2020待遇调整', '2021待遇调整', '2022待遇调整', '2023待遇调整', '补发', '借支', '应发工资', '公积', '保险扣款', '备注', '月份', '年份']
2025-07-31 18:09:25.759 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 13 行, 28 列
2025-07-31 18:09:25.759 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2678 | 表格数据设置完成: 13 行, 耗时: 34.2ms
2025-07-31 18:09:25.759 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7115 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-31 18:09:25.759 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7128 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-31 18:09:25.759 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1632 | 🆕 [列宽保存] 未找到表格 salary_data_2025_07_pension_employees 的列宽配置
2025-07-31 18:09:25.759 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 13
2025-07-31 18:09:25.771 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3400 | 🔧 [统一数据设置] 数据已成功设置到UI: 13行, 28列
2025-07-31 18:09:25.771 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3433 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=1, 总记录数=13
2025-07-31 18:09:25.773 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3439 | 🔧 [分页修复] 按钮可用性: 下一页=False, 上一页=False
2025-07-31 18:09:25.773 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3450 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-13
2025-07-31 18:09:25.775 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3721 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-07-31 18:09:25.781 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3722 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 13, 'start_record': 1, 'end_record': 13}
2025-07-31 18:09:25.782 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3723 | 🔍 [表格调试] 当前表格行数: 13
2025-07-31 18:09:25.783 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3724 | 🔍 [表格调试] 当前表格列数: 28
2025-07-31 18:09:25.784 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3726 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-13
2025-07-31 18:09:25.785 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3752 | 🔧 [分页行号修复] 表格状态: 当前行数=13, 期望行数=13
2025-07-31 18:09:25.785 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 28 个, 行号起始 1, 共 13 行
2025-07-31 18:09:25.787 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:3831 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共13行
2025-07-31 18:09:25.788 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3452 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-07-31 18:09:25.788 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3466 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-07-31 18:09:25.789 | INFO     | src.services.table_data_service:load_table_data:510 | [修复数据发布] 数据更新事件已发布
2025-07-31 18:09:25.790 | INFO     | src.gui.prototype.prototype_main_window:_refresh_data_layer:7133 | 🔧 [P1-3] 表格数据刷新成功: salary_data_2025_07_pension_employees
2025-07-31 18:09:25.797 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1553 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-07-31 18:09:25.798 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1561 | 找到工资表节点: 💰 工资表
2025-07-31 18:09:25.800 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年 > 7月 > 退休人员', '工资表 > 2025年', '工资表 > 2025年 > 7月 > A岗职工', '工资表 > 2025年 > 7月', '工资表 > 2025年 > 7月 > 离休人员']
2025-07-31 18:09:25.800 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:6034 | 导航变化: 工资表 > 2025年 > 7月 > 退休人员
2025-07-31 18:09:25.801 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8517 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_07_pension_employees -> None
2025-07-31 18:09:25.802 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8533 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-07-31 18:09:25.803 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:5941 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-07-31 18:09:25.805 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:6734 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '7月', '退休人员'] -> salary_data_2025_07_pension_employees
2025-07-31 18:09:25.810 | INFO     | src.gui.widgets.pagination_widget:reset:620 | 分页状态已重置
2025-07-31 18:09:25.810 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_07_pension_employees 的缓存
2025-07-31 18:09:25.812 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:8985 | 已注册 2 个表格到表头管理器
2025-07-31 18:09:25.813 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1084 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-07-31 18:09:25.814 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1157 | 🔧 [P1-3] 智能表头重影检测完成，耗时 1.01ms
2025-07-31 18:09:25.814 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1158 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-07-31 18:09:25.815 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:6115 | 🆕 使用新架构加载数据: salary_data_2025_07_pension_employees（通过事件系统）
2025-07-31 18:09:25.816 | INFO     | src.services.table_data_service:load_table_data:347 | 加载表格数据: salary_data_2025_07_pension_employees, 页码: 1
2025-07-31 18:09:25.817 | INFO     | src.services.table_data_service:load_table_data:370 | [缓存命中] 使用缓存数据: salary_data_2025_07_pension_employees 第1页
2025-07-31 18:09:25.818 | INFO     | src.services.table_data_service:load_table_data:376 | [根本修复] 缓存命中，发布数据更新事件: salary_data_2025_07_pension_employees, 13行
2025-07-31 18:09:25.824 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3317 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_pension_employees
2025-07-31 18:09:25.825 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3342 | 数据内容: 13行 x 32列
2025-07-31 18:09:25.825 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3368 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_07_pension_employees, 13行
2025-07-31 18:09:25.828 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_pension_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:09:25.829 | INFO     | src.gui.prototype.prototype_main_window:set_data:728 | 通过公共接口设置新的表格数据(DataFrame): 13 行
2025-07-31 18:09:25.831 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_pension_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:09:25.835 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=未知0, 薪资=N/A
2025-07-31 18:09:25.838 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=未知1, 薪资=N/A
2025-07-31 18:09:25.838 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:4558 | 最大可见行数已更新: 13 -> 13
2025-07-31 18:09:25.839 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4611 | 根据数据量(13)自动调整最大可见行数为: 13
2025-07-31 18:09:25.847 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2517 | 数据格式化完成: salary_data_2025_07_pension_employees, 类型: retired_employees
2025-07-31 18:09:25.855 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 13行数据
2025-07-31 18:09:25.856 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 13行 x 28列
2025-07-31 18:09:25.859 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时3.0ms, 平均每行0.23ms
2025-07-31 18:09:25.862 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=13, 渲染时间=3.0ms, 策略=small_dataset
2025-07-31 18:09:25.862 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-07-31 18:09:25.863 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2598 | [数据流追踪] 优化渲染完成: 13行 x 28列, 策略=small_dataset, 耗时=3.0ms, 性能评级=excellent
2025-07-31 18:09:25.864 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 未知0
2025-07-31 18:09:25.865 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 未知1
2025-07-31 18:09:25.865 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 未知2
2025-07-31 18:09:25.867 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 未知3
2025-07-31 18:09:25.868 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 未知4
2025-07-31 18:09:25.869 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 13
2025-07-31 18:09:25.869 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:474 | 🚨 [UI数据修复] 无法找到工号字段，first_row存在: True
2025-07-31 18:09:25.870 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:476 | 🚨 [UI数据修复] main_data字段: ['人员代码', '姓名', '部门名称', '人员类别代码', '基本退休费', '津贴', '结余津贴', '离退休生活补贴', '护理费', '物业补贴', '住房补贴', '增资预付', '2016待遇调整', '2017待遇调整', '2018待遇调整', '2019待遇调整', '2020待遇调整', '2021待遇调整', '2022待遇调整', '2023待遇调整', '补发', '借支', '应发工资', '公积', '保险扣款', '备注', '月份', '年份']
2025-07-31 18:09:25.876 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 13 行, 28 列
2025-07-31 18:09:25.877 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2678 | 表格数据设置完成: 13 行, 耗时: 42.8ms
2025-07-31 18:09:25.878 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7115 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-31 18:09:25.879 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7128 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-31 18:09:25.880 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1632 | 🆕 [列宽保存] 未找到表格 salary_data_2025_07_pension_employees 的列宽配置
2025-07-31 18:09:25.881 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 13
2025-07-31 18:09:25.882 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3400 | 🔧 [统一数据设置] 数据已成功设置到UI: 13行, 28列
2025-07-31 18:09:25.882 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3433 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=1, 总记录数=13
2025-07-31 18:09:25.883 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3439 | 🔧 [分页修复] 按钮可用性: 下一页=False, 上一页=False
2025-07-31 18:09:25.884 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3450 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-13
2025-07-31 18:09:25.884 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3721 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-07-31 18:09:25.893 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3722 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 13, 'start_record': 1, 'end_record': 13}
2025-07-31 18:09:25.893 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3723 | 🔍 [表格调试] 当前表格行数: 13
2025-07-31 18:09:25.894 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3724 | 🔍 [表格调试] 当前表格列数: 28
2025-07-31 18:09:25.895 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3726 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-13
2025-07-31 18:09:25.896 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3752 | 🔧 [分页行号修复] 表格状态: 当前行数=13, 期望行数=13
2025-07-31 18:09:25.896 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 28 个, 行号起始 1, 共 13 行
2025-07-31 18:09:25.897 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:3831 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共13行
2025-07-31 18:09:25.898 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3452 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-07-31 18:09:25.941 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-07-31 18:09:25.942 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:8793 | ✅ [新架构] 组件状态一致性验证通过
2025-07-31 18:09:25.946 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:8756 | 🆕 [新架构] 导航迁移完成
2025-07-31 18:09:25.946 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3466 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-07-31 18:09:25.947 | INFO     | src.services.table_data_service:load_table_data:398 | [根本修复] 缓存数据更新事件已发布，UI应该能正常更新
2025-07-31 18:09:25.948 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:6120 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-07-31 18:09:25.948 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:896 | 导航选择: 工资表 > 2025年 > 7月 > 退休人员
2025-07-31 18:09:25.950 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 4 个匹配类型 'salary_data' 的表
2025-07-31 18:09:25.951 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1646 | 创建年份节点: 2025年，包含 1 个月份
2025-07-31 18:09:25.951 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1666 | 工资数据导航已强制刷新: 1 个年份, 1 个月份
2025-07-31 18:09:25.953 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1669 | 默认路径: 工资表 > 2025年 > 7月 > 全部在职人员
2025-07-31 18:09:25.959 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1708 | force_refresh_salary_data 执行完成
2025-07-31 18:09:25.961 | INFO     | src.gui.prototype.prototype_main_window:_refresh_data_layer:7147 | 🔧 [P1-3] 导航面板数据已刷新
2025-07-31 18:09:25.961 | INFO     | src.gui.prototype.prototype_main_window:_refresh_data_layer:7157 | 🔧 [P1-3] 数据层刷新完成
2025-07-31 18:09:25.962 | INFO     | src.gui.prototype.prototype_main_window:_refresh_ui_components:7171 | 🔧 [P1-3] 开始刷新UI组件状态
2025-07-31 18:09:25.963 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_state:1020 | 🔧 [P2-2] 开始刷新导航面板状态
2025-07-31 18:09:25.964 | INFO     | src.gui.prototype.widgets.smart_search_debounce:reset_statistics:657 | 统计信息已重置
2025-07-31 18:09:25.965 | WARNING  | src.gui.prototype.widgets.enhanced_navigation_panel:_verify_navigation_state_consistency:1535 | 🔧 [P2-2] 导航状态一致性问题: ['路径应展开但未展开: 工资表 > 2025年', '路径应展开但未展开: 工资表 > 2025年 > 7月 > 退休人员', '路径应展开但未展开: 工资表 > 2025年 > 7月 > A岗职工', '路径应展开但未展开: 工资表 > 2025年 > 7月']
2025-07-31 18:09:25.966 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:refresh_navigation_state:1040 | 🔧 [P2-2] 导航面板状态刷新完成
2025-07-31 18:09:25.967 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:sync_with_global_state:1053 | 🔧 [P2-2] 开始与全局状态同步
2025-07-31 18:09:25.968 | INFO     | src.gui.widgets.pagination_widget:refresh_pagination_state:629 | 🔧 [P2-1] 开始刷新分页组件状态
2025-07-31 18:09:25.975 | INFO     | src.gui.widgets.pagination_widget:refresh_pagination_state:646 | 🔧 [P2-1] 分页状态刷新完成: 第1页/1页, 共13条记录
2025-07-31 18:09:25.976 | INFO     | src.gui.prototype.prototype_main_window:_refresh_ui_components:7237 | 🔧 [P1-3] UI组件状态刷新完成
2025-07-31 18:09:25.977 | INFO     | src.gui.prototype.prototype_main_window:_refresh_display_state:7251 | 🔧 [P1-3] 开始刷新显示状态
2025-07-31 18:09:26.153 | INFO     | src.gui.prototype.prototype_main_window:_force_global_redraw:7445 | 🔧 [P2-重影修复] 执行表头重影清理...
2025-07-31 18:09:26.153 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1553 | 开始强制刷新工资数据导航... (尝试 1/3)
2025-07-31 18:09:26.153 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1561 | 找到工资表节点: 💰 工资表
2025-07-31 18:09:26.153 | INFO     | src.modules.data_storage.dynamic_table_manager:get_table_list:885 | 找到 4 个匹配类型 'salary_data' 的表
2025-07-31 18:09:26.153 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1646 | 创建年份节点: 2025年，包含 1 个月份
2025-07-31 18:09:26.153 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1666 | 工资数据导航已强制刷新: 1 个年份, 1 个月份
2025-07-31 18:09:26.153 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1669 | 默认路径: 工资表 > 2025年 > 7月 > 全部在职人员
2025-07-31 18:09:26.153 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:force_refresh_salary_data:1708 | force_refresh_salary_data 执行完成
2025-07-31 18:09:26.153 | INFO     | src.gui.prototype.prototype_main_window:_force_global_redraw:7460 | 🔧 [组件检查] 导航面板数据已刷新
2025-07-31 18:09:26.153 | INFO     | src.gui.prototype.prototype_main_window:_force_global_redraw:7465 | 🔧 [P2-重影修复] 使用新架构刷新表格数据: salary_data_2025_07_pension_employees
2025-07-31 18:09:26.153 | INFO     | src.services.table_data_service:load_table_data:347 | 加载表格数据: salary_data_2025_07_pension_employees, 页码: 1
2025-07-31 18:09:26.153 | INFO     | src.core.unified_data_request_manager:request_table_data:203 | 开始处理数据请求: salary_data_2025_07_pension_employees, 类型: initial_load
2025-07-31 18:09:26.153 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:487 | 正在从表 salary_data_2025_07_pension_employees 分页获取数据（支持排序）: 第1页, 每页50条, 排序=0列
2025-07-31 18:09:26.153 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:547 | 🔧 [P0-CRITICAL修复] 执行的完整SQL查询: SELECT * FROM "salary_data_2025_07_pension_employees" LIMIT 50 OFFSET 0
2025-07-31 18:09:26.184 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:557 | 🔧 [P0-CRITICAL修复] 数据库查询返回的前5个工号: ['19709165.0', '19981259.0', '19721294.0', '19841258.0', '19499098.0']
2025-07-31 18:09:26.184 | INFO     | src.modules.data_storage.dynamic_table_manager:get_dataframe_paginated_with_sort:569 | 成功从表 salary_data_2025_07_pension_employees 获取第1页数据（含排序）: 13 行，总计13行
2025-07-31 18:09:26.184 | INFO     | src.core.unified_data_request_manager:request_table_data:252 | 数据请求处理完成: 32字段, 13行, 耗时31.4ms
2025-07-31 18:09:26.184 | INFO     | src.services.table_data_service:load_table_data:434 | [性能优化] 使用单例格式化管理器，避免重复初始化
2025-07-31 18:09:26.184 | INFO     | src.services.table_data_service:load_table_data:452 | [统一格式化] 数据格式化完成: salary_data_2025_07_pension_employees, 类型: retired_employees
2025-07-31 18:09:26.184 | INFO     | src.services.table_data_service:load_table_data:489 | [修复数据发布] 数据加载成功，发布更新事件: salary_data_2025_07_pension_employees, 13行
2025-07-31 18:09:26.184 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3317 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_pension_employees
2025-07-31 18:09:26.184 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3342 | 数据内容: 13行 x 32列
2025-07-31 18:09:26.184 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3368 | 🔧 [数据更新] 接收到initial_load操作的数据更新事件: salary_data_2025_07_pension_employees, 13行
2025-07-31 18:09:26.184 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_pension_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:09:26.184 | INFO     | src.gui.prototype.prototype_main_window:set_data:728 | 通过公共接口设置新的表格数据(DataFrame): 13 行
2025-07-31 18:09:26.184 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_pension_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:09:26.200 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=未知0, 薪资=N/A
2025-07-31 18:09:26.200 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=未知1, 薪资=N/A
2025-07-31 18:09:26.200 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:4558 | 最大可见行数已更新: 13 -> 13
2025-07-31 18:09:26.216 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4611 | 根据数据量(13)自动调整最大可见行数为: 13
2025-07-31 18:09:26.216 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2517 | 数据格式化完成: salary_data_2025_07_pension_employees, 类型: retired_employees
2025-07-31 18:09:26.216 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 13行数据
2025-07-31 18:09:26.216 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 13行 x 28列
2025-07-31 18:09:26.216 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时0.0ms, 平均每行0.00ms
2025-07-31 18:09:26.216 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=13, 渲染时间=0.0ms, 策略=small_dataset
2025-07-31 18:09:26.216 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-07-31 18:09:26.232 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2598 | [数据流追踪] 优化渲染完成: 13行 x 28列, 策略=small_dataset, 耗时=0.0ms, 性能评级=excellent
2025-07-31 18:09:26.232 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 未知0
2025-07-31 18:09:26.232 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 未知1
2025-07-31 18:09:26.232 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 未知2
2025-07-31 18:09:26.232 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 未知3
2025-07-31 18:09:26.232 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 未知4
2025-07-31 18:09:26.232 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 13
2025-07-31 18:09:26.232 | WARNING  | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:474 | 🚨 [UI数据修复] 无法找到工号字段，first_row存在: True
2025-07-31 18:09:26.232 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:476 | 🚨 [UI数据修复] main_data字段: ['人员代码', '姓名', '部门名称', '人员类别代码', '基本退休费', '津贴', '结余津贴', '离退休生活补贴', '护理费', '物业补贴', '住房补贴', '增资预付', '2016待遇调整', '2017待遇调整', '2018待遇调整', '2019待遇调整', '2020待遇调整', '2021待遇调整', '2022待遇调整', '2023待遇调整', '补发', '借支', '应发工资', '公积', '保险扣款', '备注', '月份', '年份']
2025-07-31 18:09:26.232 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 13 行, 28 列
2025-07-31 18:09:26.232 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2678 | 表格数据设置完成: 13 行, 耗时: 31.4ms
2025-07-31 18:09:26.232 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7115 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-31 18:09:26.247 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7128 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-31 18:09:26.247 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1632 | 🆕 [列宽保存] 未找到表格 salary_data_2025_07_pension_employees 的列宽配置
2025-07-31 18:09:26.247 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 13
2025-07-31 18:09:26.256 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3400 | 🔧 [统一数据设置] 数据已成功设置到UI: 13行, 28列
2025-07-31 18:09:26.256 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3433 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=1, 总记录数=13
2025-07-31 18:09:26.257 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3439 | 🔧 [分页修复] 按钮可用性: 下一页=False, 上一页=False
2025-07-31 18:09:26.258 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3450 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-13
2025-07-31 18:09:26.258 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3721 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-07-31 18:09:26.258 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3722 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 13, 'start_record': 1, 'end_record': 13}
2025-07-31 18:09:26.259 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3723 | 🔍 [表格调试] 当前表格行数: 13
2025-07-31 18:09:26.259 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3724 | 🔍 [表格调试] 当前表格列数: 28
2025-07-31 18:09:26.259 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3726 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-13
2025-07-31 18:09:26.260 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3752 | 🔧 [分页行号修复] 表格状态: 当前行数=13, 期望行数=13
2025-07-31 18:09:26.260 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 28 个, 行号起始 1, 共 13 行
2025-07-31 18:09:26.260 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:3831 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共13行
2025-07-31 18:09:26.261 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3452 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-07-31 18:09:26.261 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3466 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-07-31 18:09:26.261 | INFO     | src.services.table_data_service:load_table_data:510 | [修复数据发布] 数据更新事件已发布
2025-07-31 18:09:26.262 | INFO     | src.gui.prototype.prototype_main_window:_force_global_redraw:7470 | 🔧 [P2-重影修复] 数据刷新成功
2025-07-31 18:09:26.262 | INFO     | src.gui.prototype.prototype_main_window:_fix_display_brightness_after_data_refresh:7512 | 🔧 [P1-2] 开始智能显示亮度修复（增强防护）
2025-07-31 18:09:26.263 | INFO     | src.gui.prototype.prototype_main_window:_refresh_display_state:7275 | 🔧 [P1-3] 显示状态刷新完成
2025-07-31 18:09:26.272 | INFO     | src.gui.prototype.prototype_main_window:_verify_refresh_results:7289 | 🔧 [P1-3] 开始验证刷新结果
2025-07-31 18:09:26.273 | INFO     | src.gui.prototype.prototype_main_window:_verify_refresh_results:7313 | 🔧 [P1-3] 全局状态刷新成功: {'success': True, 'elapsed_time': 0.6113505363464355, 'total_stages': 8, 'completed_stages': 4, 'error_count': 0, 'errors': []}
2025-07-31 18:09:26.274 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:6857 | 🔧 [数据流追踪] 使用退休人员表头: 26个字段
2025-07-31 18:09:26.274 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6891 | 🔧 [数据流追踪] 使用表 salary_data_2025_07_pension_employees 的专用表头: 26个字段
2025-07-31 18:09:26.275 | WARNING  | src.gui.prototype.prototype_main_window:set_data:708 | 尝试设置空数据，将显示提示信息。
2025-07-31 18:09:26.275 | INFO     | src.gui.prototype.prototype_main_window:_get_table_headers_by_type:6857 | 🔧 [数据流追踪] 使用退休人员表头: 26个字段
2025-07-31 18:09:26.275 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6891 | 🔧 [数据流追踪] 使用表 salary_data_2025_07_pension_employees 的专用表头: 26个字段
2025-07-31 18:09:26.276 | WARNING  | src.gui.prototype.prototype_main_window:set_data:672 | 🔧 [P0-2] 检测到递归设置数据，跳过以防止表头重影
2025-07-31 18:09:26.276 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6910 | 已显示标准空表格，表头数量: 26
2025-07-31 18:09:26.277 | INFO     | src.gui.widgets.pagination_widget:reset:620 | 分页状态已重置
2025-07-31 18:09:26.277 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6910 | 已显示标准空表格，表头数量: 26
2025-07-31 18:09:26.764 | WARNING  | src.gui.table_header_manager:_get_current_header_labels:421 | 表格 <PyQt5.QtWidgets.QTableWidget object at 0x000002201CC508C0> 对象已被回收或不存在
2025-07-31 18:09:26.764 | WARNING  | src.gui.table_header_manager:_get_current_header_labels:421 | 表格 <src.gui.prototype.widgets.virtualized_expandable_table.VirtualizedExpandableTable object at 0x000002201BE39880> 对象已被回收或不存在
2025-07-31 18:09:26.764 | INFO     | src.gui.table_header_manager:clear_table_header_cache_enhanced:319 | 表头缓存清理完成: 2/2 个表格成功清理
2025-07-31 18:09:28.123 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 7月 > 退休人员', '工资表 > 2025年 > 7月', '工资表 > 2025年 > 7月 > A岗职工', '工资表 > 2025年 > 7月 > 离休人员']
2025-07-31 18:09:28.139 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:6034 | 导航变化: 工资表 > 2025年
2025-07-31 18:09:28.139 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8517 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_07_pension_employees -> None
2025-07-31 18:09:28.139 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8533 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-07-31 18:09:28.139 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:5941 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-07-31 18:09:28.139 | WARNING  | src.gui.prototype.prototype_main_window:set_data:688 | 尝试设置空数据，将显示提示信息。
2025-07-31 18:09:28.139 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6895 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-07-31 18:09:28.139 | WARNING  | src.gui.prototype.prototype_main_window:set_data:672 | 🔧 [P0-2] 检测到递归设置数据，跳过以防止表头重影
2025-07-31 18:09:28.139 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6910 | 已显示标准空表格，表头数量: 22
2025-07-31 18:09:28.139 | INFO     | src.gui.widgets.pagination_widget:reset:620 | 分页状态已重置
2025-07-31 18:09:28.139 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6895 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-07-31 18:09:28.139 | WARNING  | src.gui.prototype.prototype_main_window:set_data:708 | 尝试设置空数据，将显示提示信息。
2025-07-31 18:09:28.139 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6895 | 🔧 [数据流追踪] 无当前表名，使用默认在职人员表头: 22个字段
2025-07-31 18:09:28.139 | WARNING  | src.gui.prototype.prototype_main_window:set_data:672 | 🔧 [P0-2] 检测到递归设置数据，跳过以防止表头重影
2025-07-31 18:09:28.139 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6910 | 已显示标准空表格，表头数量: 22
2025-07-31 18:09:28.139 | INFO     | src.gui.widgets.pagination_widget:reset:620 | 分页状态已重置
2025-07-31 18:09:28.139 | INFO     | src.gui.prototype.prototype_main_window:_show_empty_table_with_prompt:6910 | 已显示标准空表格，表头数量: 22
2025-07-31 18:09:28.139 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:896 | 导航选择: 工资表 > 2025年
2025-07-31 18:09:29.623 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 7月 > A岗职工', '工资表 > 2025年 > 7月 > 退休人员', '工资表 > 2025年 > 7月', '工资表 > 2025年 > 7月 > 全部在职人员']
2025-07-31 18:09:29.623 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:6034 | 导航变化: 工资表 > 2025年 > 7月 > A岗职工
2025-07-31 18:09:29.623 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8517 | 🚫 [用户要求] 表格切换不显示加载条: None -> None
2025-07-31 18:09:29.623 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8533 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-07-31 18:09:29.623 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:5941 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-07-31 18:09:29.623 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:6734 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '7月', 'A岗职工'] -> salary_data_2025_07_a_grade_employees
2025-07-31 18:09:29.623 | INFO     | src.gui.widgets.pagination_widget:reset:620 | 分页状态已重置
2025-07-31 18:09:29.623 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_07_a_grade_employees 的缓存
2025-07-31 18:09:29.623 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:8985 | 已注册 2 个表格到表头管理器
2025-07-31 18:09:29.639 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1084 | 🔧 [P1-3] 开始智能表头重影检测，共 2 个表格
2025-07-31 18:09:29.639 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1157 | 🔧 [P1-3] 智能表头重影检测完成，耗时 0.00ms
2025-07-31 18:09:29.639 | INFO     | src.gui.table_header_manager:enhanced_auto_detect_and_fix_shadows:1158 | 检测结果：0 个重影表格，0 个修复成功，0 个修复失败
2025-07-31 18:09:29.639 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:6115 | 🆕 使用新架构加载数据: salary_data_2025_07_a_grade_employees（通过事件系统）
2025-07-31 18:09:29.639 | INFO     | src.services.table_data_service:load_table_data:347 | 加载表格数据: salary_data_2025_07_a_grade_employees, 页码: 1
2025-07-31 18:09:29.639 | INFO     | src.services.table_data_service:load_table_data:370 | [缓存命中] 使用缓存数据: salary_data_2025_07_a_grade_employees 第1页
2025-07-31 18:09:29.639 | INFO     | src.services.table_data_service:load_table_data:376 | [根本修复] 缓存命中，发布数据更新事件: salary_data_2025_07_a_grade_employees, 50行
2025-07-31 18:09:29.639 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3317 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_a_grade_employees
2025-07-31 18:09:29.639 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3342 | 数据内容: 50行 x 26列
2025-07-31 18:09:29.639 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3368 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_07_a_grade_employees, 50行
2025-07-31 18:09:29.639 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_a_grade_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:09:29.639 | INFO     | src.gui.prototype.prototype_main_window:set_data:728 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-31 18:09:29.639 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_a_grade_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:09:29.639 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=34660024.0, 薪资=N/A
2025-07-31 18:09:29.639 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20222002.0, 薪资=N/A
2025-07-31 18:09:29.639 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:4558 | 最大可见行数已更新: 13 -> 50
2025-07-31 18:09:29.639 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-31 18:09:29.654 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2517 | 数据格式化完成: salary_data_2025_07_a_grade_employees, 类型: active_employees
2025-07-31 18:09:29.670 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-07-31 18:09:29.670 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 50行 x 22列
2025-07-31 18:09:29.670 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时0.0ms, 平均每行0.00ms
2025-07-31 18:09:29.670 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=0.0ms, 策略=small_dataset
2025-07-31 18:09:29.670 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-07-31 18:09:29.670 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2598 | [数据流追踪] 优化渲染完成: 50行 x 22列, 策略=small_dataset, 耗时=0.0ms, 性能评级=excellent
2025-07-31 18:09:29.670 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 34660024
2025-07-31 18:09:29.670 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 20222002
2025-07-31 18:09:29.670 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 14660141
2025-07-31 18:09:29.670 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 34660002
2025-07-31 18:09:29.670 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 34660010
2025-07-31 18:09:29.670 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-07-31 18:09:29.670 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[0]: 工号=34660024, 薪资=N/A
2025-07-31 18:09:29.670 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[1]: 工号=20222002, 薪资=N/A
2025-07-31 18:09:29.670 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[2]: 工号=14660141, 薪资=N/A
2025-07-31 18:09:29.670 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[3]: 工号=34660002, 薪资=N/A
2025-07-31 18:09:29.670 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[4]: 工号=34660010, 薪资=N/A
2025-07-31 18:09:29.670 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:472 | 可见行数据顺序: ['34660024', '20222002', '14660141', '34660002', '34660010']
2025-07-31 18:09:29.670 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 50 行, 22 列
2025-07-31 18:09:29.670 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:409 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_07_a_grade_employees
2025-07-31 18:09:29.670 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:440 | 🔧 [新架构] 为表格 salary_data_2025_07_a_grade_employees 重新加载 26 个字段映射
2025-07-31 18:09:29.686 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2678 | 表格数据设置完成: 50 行, 耗时: 31.4ms
2025-07-31 18:09:29.686 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7115 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-31 18:09:29.686 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7128 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-31 18:09:29.686 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1632 | 🆕 [列宽保存] 未找到表格 salary_data_2025_07_a_grade_employees 的列宽配置
2025-07-31 18:09:29.686 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 50
2025-07-31 18:09:29.698 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3400 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 22列
2025-07-31 18:09:29.698 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 62
2025-07-31 18:09:29.699 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3414 | 🔧 [分页修复] 数据更新事件设置总记录数: 62
2025-07-31 18:09:29.699 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3433 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=2, 总记录数=62
2025-07-31 18:09:29.699 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3439 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=False
2025-07-31 18:09:29.699 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3450 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-50
2025-07-31 18:09:29.700 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3721 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-07-31 18:09:29.700 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3722 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 62, 'start_record': 1, 'end_record': 50}
2025-07-31 18:09:29.701 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3723 | 🔍 [表格调试] 当前表格行数: 50
2025-07-31 18:09:29.702 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3724 | 🔍 [表格调试] 当前表格列数: 22
2025-07-31 18:09:29.702 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3726 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-07-31 18:09:29.702 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3752 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-07-31 18:09:29.703 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 22 个, 行号起始 1, 共 50 行
2025-07-31 18:09:29.703 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:3831 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-07-31 18:09:29.704 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3452 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-07-31 18:09:29.761 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-07-31 18:09:29.762 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:8793 | ✅ [新架构] 组件状态一致性验证通过
2025-07-31 18:09:29.764 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:8756 | 🆕 [新架构] 导航迁移完成
2025-07-31 18:09:29.765 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3466 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-07-31 18:09:29.765 | INFO     | src.services.table_data_service:load_table_data:398 | [根本修复] 缓存数据更新事件已发布，UI应该能正常更新
2025-07-31 18:09:29.766 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:6120 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-07-31 18:09:29.766 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:896 | 导航选择: 工资表 > 2025年 > 7月 > A岗职工
2025-07-31 18:09:31.077 | INFO     | src.gui.prototype.widgets.smart_tree_expansion:predict_expansion_paths:214 | 预测展开路径: ['工资表 > 2025年', '工资表 > 2025年 > 7月 > 退休人员', '工资表 > 2025年 > 7月 > A岗职工', '工资表 > 2025年 > 7月', '工资表 > 2025年 > 7月 > 全部在职人员']
2025-07-31 18:09:31.077 | INFO     | src.gui.prototype.prototype_main_window:_on_navigation_changed:6034 | 导航变化: 工资表 > 2025年 > 7月 > 全部在职人员
2025-07-31 18:09:31.077 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8517 | 🚫 [用户要求] 表格切换不显示加载条: salary_data_2025_07_a_grade_employees -> None
2025-07-31 18:09:31.077 | INFO     | src.gui.prototype.prototype_main_window:_complete_table_reset_on_navigation:8533 | 🆕 [新架构] 渐进式状态迁移准备完成，等待数据加载
2025-07-31 18:09:31.077 | INFO     | src.gui.prototype.prototype_main_window:clear_field_processing_cache:5941 | 🔧 [缓存清理] 清理了所有 0 个字段处理缓存条目
2025-07-31 18:09:31.077 | INFO     | src.gui.prototype.prototype_main_window:_generate_table_name_from_path:6734 | 🔧 [表名生成] 从完整路径生成表名: ['工资表', '2025年', '7月', '全部在职人员'] -> salary_data_2025_07_active_employees
2025-07-31 18:09:31.077 | INFO     | src.gui.widgets.pagination_widget:reset:620 | 分页状态已重置
2025-07-31 18:09:31.077 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:362 | 已清理表 salary_data_2025_07_active_employees 的缓存
2025-07-31 18:09:31.077 | INFO     | src.gui.prototype.prototype_main_window:_register_all_tables_to_header_manager:8985 | 已注册 2 个表格到表头管理器
2025-07-31 18:09:31.077 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:6115 | 🆕 使用新架构加载数据: salary_data_2025_07_active_employees（通过事件系统）
2025-07-31 18:09:31.077 | INFO     | src.services.table_data_service:load_table_data:347 | 加载表格数据: salary_data_2025_07_active_employees, 页码: 1
2025-07-31 18:09:31.077 | INFO     | src.services.table_data_service:load_table_data:370 | [缓存命中] 使用缓存数据: salary_data_2025_07_active_employees 第1页
2025-07-31 18:09:31.077 | INFO     | src.services.table_data_service:load_table_data:376 | [根本修复] 缓存命中，发布数据更新事件: salary_data_2025_07_active_employees, 50行
2025-07-31 18:09:31.077 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3317 | 🆕 接收到新架构数据更新事件: salary_data_2025_07_active_employees
2025-07-31 18:09:31.077 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3342 | 数据内容: 50行 x 28列
2025-07-31 18:09:31.077 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3368 | 🔧 [数据更新] 接收到cached_load操作的数据更新事件: salary_data_2025_07_active_employees, 50行
2025-07-31 18:09:31.077 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:09:31.077 | INFO     | src.gui.prototype.prototype_main_window:set_data:728 | 通过公共接口设置新的表格数据(DataFrame): 50 行
2025-07-31 18:09:31.093 | INFO     | src.gui.prototype.prototype_main_window:_apply_table_field_preference:6020 | 表 salary_data_2025_07_active_employees 没有用户偏好设置，显示所有可见字段
2025-07-31 18:09:31.093 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第0行: 工号=19990089.0, 薪资=2375.0
2025-07-31 18:09:31.093 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2439 | 🚨 [UI数据修复] 保存到original_data第1行: 工号=20161565.0, 薪资=1696.0
2025-07-31 18:09:31.093 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_max_visible_rows:4558 | 最大可见行数已更新: 50 -> 50
2025-07-31 18:09:31.093 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:auto_adjust_max_visible_rows:4611 | 根据数据量(50)自动调整最大可见行数为: 50
2025-07-31 18:09:31.108 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2517 | 数据格式化完成: salary_data_2025_07_active_employees, 类型: active_employees
2025-07-31 18:09:31.123 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:238 | [数据流追踪] 自动渲染策略选择: 50行数据
2025-07-31 18:09:31.123 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:51 | [数据流追踪] 开始小数据集渲染: 50行 x 24列
2025-07-31 18:09:31.123 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_small_dataset:104 | [数据流追踪] 小数据集渲染完成: 耗时0.0ms, 平均每行0.00ms
2025-07-31 18:09:31.123 | INFO     | src.core.performance_metrics_collector:record_operation:85 | [数据流追踪] 记录性能度量: render, 数据大小=50, 渲染时间=0.0ms, 策略=small_dataset
2025-07-31 18:09:31.123 | INFO     | src.gui.prototype.widgets.optimized_table_renderer:render_with_auto_strategy:259 | [数据流追踪] 选择小数据集渲染策略
2025-07-31 18:09:31.123 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2598 | [数据流追踪] 优化渲染完成: 50行 x 24列, 策略=small_dataset, 耗时=0.0ms, 性能评级=excellent
2025-07-31 18:09:31.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第0行数据工号: 19990089
2025-07-31 18:09:31.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第1行数据工号: 20161565
2025-07-31 18:09:31.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第2行数据工号: 20191782
2025-07-31 18:09:31.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第3行数据工号: 20151515
2025-07-31 18:09:31.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:430 | 第4行数据工号: 20181640
2025-07-31 18:09:31.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:458 | 🚨 [UI数据修复] _update_visible_rows调用，数据行数: 50
2025-07-31 18:09:31.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[0]: 工号=19990089, 薪资=2375
2025-07-31 18:09:31.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[1]: 工号=20161565, 薪资=1696
2025-07-31 18:09:31.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[2]: 工号=20191782, 薪资=1427
2025-07-31 18:09:31.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[3]: 工号=20151515, 薪资=2175
2025-07-31 18:09:31.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:471 | 🚨 [UI数据修复] row_data[4]: 工号=20181640, 薪资=2582
2025-07-31 18:09:31.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_update_visible_rows:472 | 可见行数据顺序: ['19990089', '20161565', '20191782', '20151515', '20181640']
2025-07-31 18:09:31.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:435 | 表格数据已设置: 50 行, 24 列
2025-07-31 18:09:31.139 | INFO     | src.gui.prototype.widgets.column_sort_manager:update_table_context:409 | 🔧 [新架构] 排序管理器切换到表格: salary_data_2025_07_active_employees
2025-07-31 18:09:31.139 | INFO     | src.gui.prototype.widgets.column_sort_manager:_reload_field_mapping_for_table:440 | 🔧 [新架构] 为表格 salary_data_2025_07_active_employees 重新加载 28 个字段映射
2025-07-31 18:09:31.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_data:2678 | 表格数据设置完成: 50 行, 耗时: 46.5ms
2025-07-31 18:09:31.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7115 | 🔧 [P0-排序修复] 开始恢复排序指示器显示
2025-07-31 18:09:31.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_restore_sort_indicators_after_data_reload:7128 | 🔧 [P0-排序修复] 无排序状态需要恢复
2025-07-31 18:09:31.139 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:restore_column_widths:1657 | 列宽已恢复: salary_data_2025_07_active_employees (24/24 列)
2025-07-31 18:09:31.139 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 50
2025-07-31 18:09:31.154 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3400 | 🔧 [统一数据设置] 数据已成功设置到UI: 50行, 24列
2025-07-31 18:09:31.164 | INFO     | src.gui.widgets.pagination_widget:set_total_records:515 | 总记录数设置为: 1396
2025-07-31 18:09:31.164 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3414 | 🔧 [分页修复] 数据更新事件设置总记录数: 1396
2025-07-31 18:09:31.164 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3433 | 🔧 [分页修复] 分页状态: 当前页=1, 总页数=28, 总记录数=1396
2025-07-31 18:09:31.165 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3439 | 🔧 [分页修复] 按钮可用性: 下一页=True, 上一页=False
2025-07-31 18:09:31.165 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3450 | 🔍 [FINAL修复] 最终调用set_pagination_state: 第1页, 记录1-50
2025-07-31 18:09:31.166 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3721 | 🔍 [表格调试] ================== set_pagination_state 开始 ==================
2025-07-31 18:09:31.166 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3722 | 🔍 [表格调试] 收到分页状态参数: {'current_page': 1, 'page_size': 50, 'total_records': 1396, 'start_record': 1, 'end_record': 50}
2025-07-31 18:09:31.167 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3723 | 🔍 [表格调试] 当前表格行数: 50
2025-07-31 18:09:31.167 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3724 | 🔍 [表格调试] 当前表格列数: 24
2025-07-31 18:09:31.167 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3726 | 🔧 [分页行号修复] 接收分页状态: 第1页, 记录1-50
2025-07-31 18:09:31.168 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:set_pagination_state:3752 | 🔧 [分页行号修复] 表格状态: 当前行数=50, 期望行数=50
2025-07-31 18:09:31.168 | INFO     | src.gui.prototype.widgets.header_update_manager:force_update_all:317 | 智能强制更新: 表头 24 个, 行号起始 1, 共 50 行
2025-07-31 18:09:31.169 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_force_update_pagination_row_numbers:3831 | 🔧 [分页行号修复] 强制更新完成: 起始记录1, 共50行
2025-07-31 18:09:31.169 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3452 | 🔍 [FINAL修复] set_pagination_state最终调用完成
2025-07-31 18:09:31.219 | INFO     | src.gui.widgets.pagination_cache_manager:clear_cache:379 | 已清理所有缓存
2025-07-31 18:09:31.219 | INFO     | src.gui.prototype.prototype_main_window:_validate_component_state_consistency:8793 | ✅ [新架构] 组件状态一致性验证通过
2025-07-31 18:09:31.221 | INFO     | src.gui.prototype.prototype_main_window:complete_navigation_transition:8756 | 🆕 [新架构] 导航迁移完成
2025-07-31 18:09:31.222 | INFO     | src.gui.prototype.prototype_main_window:_on_new_data_updated:3466 | 🆕 [新架构] 数据更新完成，已完成渐进式状态迁移
2025-07-31 18:09:31.222 | INFO     | src.services.table_data_service:load_table_data:398 | [根本修复] 缓存数据更新事件已发布，UI应该能正常更新
2025-07-31 18:09:31.222 | INFO     | src.gui.prototype.prototype_main_window:_check_and_load_data_with_pagination:6120 | 🆕 新架构数据加载请求已发送，等待事件系统处理
2025-07-31 18:09:31.223 | INFO     | src.gui.prototype.widgets.enhanced_navigation_panel:_on_tree_selection_changed:896 | 导航选择: 工资表 > 2025年 > 7月 > 全部在职人员
2025-07-31 18:09:31.451 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1597 | 🔧 [列宽保存修复] 准备保存列宽到文件: C:\test\salary_changes\salary_changes\state\column_widths.json
2025-07-31 18:09:31.451 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1598 | 🔧 [列宽保存修复] 表名: salary_data_2025_07_active_employees, 列数: 24
2025-07-31 18:09:31.451 | INFO     | src.gui.prototype.widgets.virtualized_expandable_table:_save_column_widths:1603 | 🔧 [列宽保存修复] 列宽设置保存成功！文件大小: 1901 字节
2025-07-31 18:10:03.072 | INFO     | __main__:main:445 | 应用程序正常退出
2025-07-31 18:10:03.077 | INFO     | src.gui.table_header_manager:cleanup_callback:131 | 🔧 [P1-1] 表格 table_0_2336944883904 已自动清理（弱引用回调）
