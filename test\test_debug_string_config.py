#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试字符串格式配置问题
"""

import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

def test_debug_string_config():
    """调试字符串格式配置"""
    print("🔍 [调试] 开始调试字符串格式配置...")
    print("=" * 60)
    
    try:
        from src.modules.format_management.format_config import FormatConfig
        
        # 1. 加载配置
        config = FormatConfig("state/format_config.json")
        
        # 2. 测试默认字符串配置
        default_string_config = config.get_format_rules('string')
        print(f"📋 [默认配置] string格式配置: {default_string_config}")
        
        # 3. 测试表特定字符串配置
        table_string_config = config.get_format_rules('string', 'retired_employees')
        print(f"📋 [表特定配置] retired_employees string格式配置: {table_string_config}")
        
        # 4. 直接检查配置路径
        retired_config = config.get_config('retired_employees_format_config')
        print(f"📋 [直接配置] retired_employees_format_config: {retired_config}")
        
        if retired_config:
            format_rules = retired_config.get('format_rules', {})
            string_format = format_rules.get('string_format', {})
            print(f"📋 [格式规则] string_format: {string_format}")
            empty_display = string_format.get('empty_display', 'NOT_FOUND')
            print(f"📋 [空值显示] empty_display: '{empty_display}'")
        
        # 5. 测试配置获取路径
        test_path = 'retired_employees_format_config.format_rules.string_format'
        path_result = config.get_config(test_path)
        print(f"📋 [路径测试] {test_path}: {path_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ [调试失败] 调试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_debug_string_config()
