#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合格式化修复验证测试

验证所有格式化问题的修复情况：
1. 人员代码字段：浮点数转字符串去除小数点
2. 浮点数字段：空值显示为0.00
3. 月份字段：提取后两位数字
4. 备注字段：空值显示为空字符串
5. 格式化验证逻辑一致性
"""

import pandas as pd
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

def test_comprehensive_format_fix():
    """综合测试格式化修复"""
    print("🔧 [综合测试] 开始验证格式化修复...")
    print("=" * 60)
    
    try:
        # 1. 创建测试数据（模拟用户遇到的问题）
        test_data = pd.DataFrame({
            '人员代码': [19990089.0, 19990090.0, None],
            '姓名': ['张三', '李四', '王五'],
            '增发一次性生活补贴': [None, 0.0, 100.5],
            '补发': [None, 0.0, 200.75],
            '借支': [None, 0.00, 300.25],
            '月份': [202501.0, 202502.0, None],
            '备注': [None, '', '测试备注']
        })
        
        print("📊 [测试数据] 原始数据:")
        print(test_data)
        print()
        
        # 2. 使用UnifiedFormatManager进行格式化
        from src.modules.format_management.unified_format_manager import UnifiedFormatManager
        format_manager = UnifiedFormatManager()
        
        formatted_data = format_manager.format_data(test_data, "retired_employees", data_source="test")
        
        print("✨ [格式化结果] 格式化后数据:")
        print(formatted_data)
        print()
        
        # 3. 验证格式化完整性
        from src.modules.format_management.master_format_manager import get_master_format_manager
        master_formatter = get_master_format_manager()
        
        is_complete = master_formatter._verify_formatting_completeness(formatted_data, "retired_employees")
        print(f"🔍 [验证结果] 格式化完整性验证: {'✅ 通过' if is_complete else '❌ 失败'}")
        print()
        
        # 4. 详细验证每个字段
        print("🎯 [详细验证] 逐字段检查:")
        
        # 验证人员代码字段
        employee_codes = formatted_data['人员代码'].tolist()
        print(f"  人员代码: {employee_codes}")
        for code in employee_codes:
            if code != '-' and '.' in str(code):
                print(f"    ❌ 人员代码包含小数点: {code}")
                return False
        print("    ✅ 人员代码格式正确（无小数点）")
        
        # 验证浮点数字段
        float_fields = ['增发一次性生活补贴', '补发', '借支']
        for field in float_fields:
            values = formatted_data[field].tolist()
            print(f"  {field}: {values}")
            for value in values:
                if value == '-':
                    print(f"    ❌ {field}空值显示为'-'而不是'0.00'")
                    return False
                elif str(value) not in ['0.00', '100.50', '200.75', '300.25'] and value != '-':
                    if not (isinstance(value, str) and '.' in value and len(value.split('.')[-1]) == 2):
                        print(f"    ❌ {field}格式不正确: {value}")
                        return False
        print("    ✅ 浮点数字段格式正确（空值为0.00，保留两位小数）")
        
        # 验证月份字段
        months = formatted_data['月份'].tolist()
        print(f"  月份: {months}")
        for month in months:
            if month != '' and month != '-':
                if len(str(month)) != 2 or not str(month).isdigit():
                    print(f"    ❌ 月份格式不正确: {month}")
                    return False
        print("    ✅ 月份字段格式正确（两位数字）")
        
        # 验证备注字段
        remarks = formatted_data['备注'].tolist()
        print(f"  备注: {remarks}")
        for remark in remarks:
            if remark == '-':
                print(f"    ❌ 备注空值显示为'-'而不是空字符串")
                return False
        print("    ✅ 备注字段格式正确（空值为空字符串）")
        
        print()
        print("🎉 [测试结果] 所有格式化问题已修复！")
        print("=" * 60)
        return True
        
    except Exception as e:
        print(f"❌ [测试失败] 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_comprehensive_format_fix()
    if success:
        print("\n✅ 综合格式化修复测试通过")
        exit(0)
    else:
        print("\n❌ 综合格式化修复测试失败")
        exit(1)
