#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
离休人员配置简单验证测试
"""

import sys
import json
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_field_registry_file():
    """测试字段注册文件内容"""
    print("🧪 测试字段注册文件内容")
    
    try:
        # 直接读取字段注册文件
        registry_file = project_root / "src" / "modules" / "format_management" / "field_registry.py"
        
        with open(registry_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否包含离休人员配置
        if '"retired_employees"' in content:
            print("  ✅ 找到离休人员配置")
            
            # 检查浮点型字段
            float_fields = [
                "basic_retirement_salary", "balance_allowance", "living_allowance",
                "housing_allowance", "property_allowance", "retirement_allowance"
            ]
            
            found_fields = 0
            for field in float_fields:
                if f'"{field}": "float"' in content:
                    found_fields += 1
            
            print(f"  ✅ 浮点型字段配置: {found_fields}/{len(float_fields)}")
            
            # 检查特殊字段
            if '"year": "year_string"' in content:
                print("  ✅ 年份字段配置正确")
            if '"month": "month_string"' in content:
                print("  ✅ 月份字段配置正确")
            
            # 检查隐藏字段
            if '"hidden_fields"' in content:
                print("  ✅ 隐藏字段配置存在")
            
            return True
        else:
            print("  ❌ 未找到离休人员配置")
            return False
            
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def test_format_config_file():
    """测试格式配置文件"""
    print("\n🧪 测试格式配置文件")
    
    try:
        config_file = project_root / "state" / "format_config.json"
        
        if not config_file.exists():
            print(f"  ❌ 配置文件不存在: {config_file}")
            return False
        
        with open(config_file, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查浮点型配置
        float_config = config.get("default_formats", {}).get("float", {})
        if float_config.get("decimal_places") == 2:
            print("  ✅ 浮点型小数位数配置正确")
        if float_config.get("zero_display") == "0.00":
            print("  ✅ 浮点型零值显示配置正确")
        if float_config.get("thousand_separator") == "":
            print("  ✅ 浮点型千分位分隔符配置正确")
        
        # 检查字符串配置
        string_config = config.get("default_formats", {}).get("string", {})
        if string_config.get("empty_display") == "":
            print("  ✅ 字符串空值显示配置正确")
        
        # 检查离休人员专门配置
        retired_config = config.get("retired_employees_format_config", {})
        if retired_config:
            print("  ✅ 离休人员专门配置存在")
            
            float_fields = retired_config.get("field_formats", {}).get("float_fields", [])
            expected_fields = ["基本离休费", "结余津贴", "生活补贴", "住房补贴", "物业补贴"]
            found_count = sum(1 for field in expected_fields if field in float_fields)
            print(f"  ✅ 离休人员浮点型字段: {found_count}/{len(expected_fields)}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def test_unified_format_manager():
    """测试统一格式管理器"""
    print("\n🧪 测试统一格式管理器")
    
    try:
        from src.modules.format_management.unified_format_manager import UnifiedFormatManager
        
        # 创建管理器实例
        manager = UnifiedFormatManager()
        
        print("  ✅ 统一格式管理器创建成功")
        
        # 测试格式化方法是否存在
        if hasattr(manager, 'format_data'):
            print("  ✅ format_data 方法存在")
        if hasattr(manager, 'format_headers'):
            print("  ✅ format_headers 方法存在")
        if hasattr(manager, 'format_table_complete'):
            print("  ✅ format_table_complete 方法存在")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🔧 离休人员配置简单验证测试")
    print("=" * 40)
    
    tests = [
        test_field_registry_file,
        test_format_config_file,
        test_unified_format_manager
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"  ❌ 测试执行失败: {e}")
    
    print("\n📊 测试总结")
    print("=" * 20)
    success_rate = passed / total * 100
    
    if passed == total:
        print(f"🎉 所有测试通过！({passed}/{total}, {success_rate:.1f}%)")
        return True
    else:
        print(f"⚠️ 部分测试失败 ({passed}/{total}, {success_rate:.1f}%)")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
