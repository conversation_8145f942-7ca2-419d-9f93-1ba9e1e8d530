#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分页和排序性能优化解决方案
修复关键性能瓶颈，提升用户体验
"""

import time
import threading
from typing import Dict, Any, Optional, List
from functools import lru_cache
from PyQt5.QtCore import QObject, pyqtSignal, QTimer, QThread, pyqtSlot
from PyQt5.QtWidgets import QApplication
from src.utils.log_config import setup_logger


class PerformanceOptimizedDataLoader(QObject):
    """性能优化的数据加载器
    
    解决方案：
    1. 单例化格式化管理器
    2. 智能缓存机制
    3. 异步数据加载
    4. 排序结果缓存
    """
    
    # 信号定义
    data_loaded = pyqtSignal(object, dict)  # 数据加载完成信号
    loading_started = pyqtSignal()  # 加载开始信号
    loading_finished = pyqtSignal()  # 加载结束信号
    
    def __init__(self):
        super().__init__()
        self.logger = setup_logger(__name__ + ".PerformanceOptimizedDataLoader")
        
        # 🎯 [性能优化] 单例化管理器，避免重复初始化
        self._master_formatter = None
        self._format_config_manager = None
        
        # 🚀 [缓存优化] 智能缓存系统
        self._data_cache = {}
        self._sort_cache = {}
        self._cache_timestamps = {}
        self._max_cache_size = 50
        self._cache_ttl = 300  # 5分钟TTL
        
        # 🔄 [异步优化] 数据加载线程池
        self._loading_threads = {}
        self._active_requests = set()
        
        self.logger.info("🚀 [性能优化] 性能优化数据加载器初始化完成")

    @property
    def master_formatter(self):
        """🎯 [单例优化] 单例化格式化管理器"""
        if self._master_formatter is None:
            try:
                from src.modules.format_management.master_format_manager import get_master_format_manager
                self._master_formatter = get_master_format_manager()
                self.logger.info("🎯 [单例优化] 格式化管理器已初始化（单例模式）")
            except Exception as e:
                self.logger.error(f"🎯 [单例优化] 格式化管理器初始化失败: {e}")
                self._master_formatter = None
        return self._master_formatter

    @property
    def format_config_manager(self):
        """🎯 [单例优化] 单例化格式配置管理器"""
        if self._format_config_manager is None:
            try:
                from src.modules.format_management.format_config import FormatConfigManager
                self._format_config_manager = FormatConfigManager()
                self.logger.info("🎯 [单例优化] 格式配置管理器已初始化（单例模式）")
            except Exception as e:
                self.logger.error(f"🎯 [单例优化] 格式配置管理器初始化失败: {e}")
                self._format_config_manager = None
        return self._format_config_manager

    def _generate_cache_key(self, table_name: str, page: int, page_size: int, 
                           sort_columns: Optional[List[Dict]] = None) -> str:
        """🚀 [缓存优化] 生成智能缓存键"""
        sort_key = ""
        if sort_columns:
            sort_parts = []
            for col in sort_columns:
                col_name = col.get('column_name', '')
                order = col.get('order', 'asc')
                sort_parts.append(f"{col_name}:{order}")
            sort_key = "|".join(sort_parts)
        
        return f"{table_name}_{page}_{page_size}_{sort_key}"

    def _is_cache_valid(self, cache_key: str) -> bool:
        """🚀 [缓存优化] 检查缓存是否有效"""
        if cache_key not in self._cache_timestamps:
            return False
        
        timestamp = self._cache_timestamps[cache_key]
        return (time.time() - timestamp) < self._cache_ttl

    def _cleanup_cache(self):
        """🚀 [缓存优化] 清理过期缓存"""
        current_time = time.time()
        expired_keys = []
        
        for cache_key, timestamp in self._cache_timestamps.items():
            if (current_time - timestamp) > self._cache_ttl:
                expired_keys.append(cache_key)
        
        for key in expired_keys:
            self._data_cache.pop(key, None)
            self._sort_cache.pop(key, None)
            self._cache_timestamps.pop(key, None)
        
        # LRU清理：如果缓存过大，移除最老的条目
        if len(self._data_cache) > self._max_cache_size:
            sorted_items = sorted(self._cache_timestamps.items(), key=lambda x: x[1])
            remove_count = len(self._data_cache) - self._max_cache_size
            
            for i in range(remove_count):
                key = sorted_items[i][0]
                self._data_cache.pop(key, None)
                self._sort_cache.pop(key, None)
                self._cache_timestamps.pop(key, None)
        
        if expired_keys:
            self.logger.info(f"🚀 [缓存优化] 清理了 {len(expired_keys)} 个过期缓存条目")

    def get_cached_data(self, table_name: str, page: int, page_size: int,
                       sort_columns: Optional[List[Dict]] = None) -> Optional[Any]:
        """🚀 [缓存优化] 获取缓存数据"""
        cache_key = self._generate_cache_key(table_name, page, page_size, sort_columns)
        
        if cache_key in self._data_cache and self._is_cache_valid(cache_key):
            self.logger.info(f"🚀 [缓存命中] {table_name} 第{page}页")
            return self._data_cache[cache_key]
        
        return None

    def cache_data(self, table_name: str, page: int, page_size: int, data: Any,
                  sort_columns: Optional[List[Dict]] = None):
        """🚀 [缓存优化] 缓存数据"""
        cache_key = self._generate_cache_key(table_name, page, page_size, sort_columns)
        
        self._data_cache[cache_key] = data
        self._cache_timestamps[cache_key] = time.time()
        
        # 定期清理缓存
        if len(self._data_cache) % 10 == 0:
            self._cleanup_cache()
        
        self.logger.info(f"🚀 [缓存存储] {table_name} 第{page}页已缓存")

    def load_data_async(self, table_name: str, page: int, page_size: int = 50,
                       sort_columns: Optional[List[Dict]] = None):
        """🔄 [异步优化] 异步加载数据"""
        request_id = f"{table_name}_{page}_{int(time.time())}"
        
        # 检查是否已有相同请求在处理
        current_request = f"{table_name}_{page}"
        if current_request in self._active_requests:
            self.logger.info(f"🔄 [请求去重] 跳过重复请求: {current_request}")
            return
        
        # 先检查缓存
        cached_data = self.get_cached_data(table_name, page, page_size, sort_columns)
        if cached_data is not None:
            # 缓存命中，立即返回
            QTimer.singleShot(0, lambda: self.data_loaded.emit(cached_data, {
                'from_cache': True,
                'table_name': table_name,
                'page': page,
                'page_size': page_size
            }))
            return

        # 标记请求为活跃状态
        self._active_requests.add(current_request)
        self.loading_started.emit()
        
        # 创建数据加载线程
        thread = DataLoadingThread(
            request_id, table_name, page, page_size, sort_columns,
            self.master_formatter
        )
        
        # 连接信号
        thread.data_ready.connect(self._on_data_ready)
        thread.loading_error.connect(self._on_loading_error)
        thread.finished.connect(lambda: self._on_thread_finished(request_id, current_request))
        
        # 启动线程
        self._loading_threads[request_id] = thread
        thread.start()
        
        self.logger.info(f"🔄 [异步加载] 启动数据加载线程: {request_id}")

    @pyqtSlot(str, object, dict)
    def _on_data_ready(self, request_id: str, data: Any, metadata: Dict):
        """🔄 [异步优化] 数据加载完成处理"""
        try:
            table_name = metadata.get('table_name')
            page = metadata.get('page')
            page_size = metadata.get('page_size')
            sort_columns = metadata.get('sort_columns')
            
            # 缓存数据
            if table_name and page and page_size:
                self.cache_data(table_name, page, page_size, data, sort_columns)
            
            # 发射数据加载完成信号
            metadata['from_cache'] = False
            self.data_loaded.emit(data, metadata)
            
            self.logger.info(f"🔄 [异步完成] 数据加载完成: {request_id}")
            
        except Exception as e:
            self.logger.error(f"🔄 [异步错误] 数据处理失败: {e}")

    @pyqtSlot(str, str)
    def _on_loading_error(self, request_id: str, error_msg: str):
        """🔄 [异步优化] 数据加载错误处理"""
        self.logger.error(f"🔄 [异步错误] 数据加载失败: {request_id} - {error_msg}")

    def _on_thread_finished(self, request_id: str, current_request: str):
        """🔄 [异步优化] 线程完成清理"""
        try:
            # 清理线程
            thread = self._loading_threads.pop(request_id, None)
            if thread:
                thread.deleteLater()
            
            # 移除活跃请求标记
            self._active_requests.discard(current_request)
            
            # 如果没有活跃请求，发射加载完成信号
            if not self._active_requests:
                self.loading_finished.emit()
            
            self.logger.info(f"🔄 [线程清理] 线程已清理: {request_id}")
            
        except Exception as e:
            self.logger.error(f"🔄 [清理错误] 线程清理失败: {e}")

    def clear_cache(self, table_name: Optional[str] = None):
        """🚀 [缓存管理] 清理缓存"""
        if table_name:
            # 清理指定表的缓存
            keys_to_remove = [key for key in self._data_cache.keys() if key.startswith(f"{table_name}_")]
            for key in keys_to_remove:
                self._data_cache.pop(key, None)
                self._sort_cache.pop(key, None)
                self._cache_timestamps.pop(key, None)
            self.logger.info(f"🚀 [缓存清理] 已清理表 {table_name} 的缓存")
        else:
            # 清理所有缓存
            self._data_cache.clear()
            self._sort_cache.clear()
            self._cache_timestamps.clear()
            self.logger.info("🚀 [缓存清理] 已清理所有缓存")


class DataLoadingThread(QThread):
    """🔄 [异步优化] 数据加载线程"""
    
    data_ready = pyqtSignal(str, object, dict)  # request_id, data, metadata
    loading_error = pyqtSignal(str, str)  # request_id, error_msg
    
    def __init__(self, request_id: str, table_name: str, page: int, page_size: int,
                 sort_columns: Optional[List[Dict]], master_formatter):
        super().__init__()
        self.request_id = request_id
        self.table_name = table_name
        self.page = page
        self.page_size = page_size
        self.sort_columns = sort_columns
        self.master_formatter = master_formatter
        self.logger = setup_logger(__name__ + ".DataLoadingThread")

    def run(self):
        """🔄 [异步执行] 数据加载执行"""
        try:
            start_time = time.time()
            
            # 模拟数据加载（替换为实际的数据服务调用）
            from src.services.table_data_service import TableDataService
            
            # 获取表格数据服务实例
            # 注意：这里需要确保线程安全
            data_service = TableDataService()
            
            # 加载数据
            response = data_service.load_table_data(
                table_name=self.table_name,
                page=self.page,
                page_size=self.page_size,
                sort_columns=self.sort_columns,
                force_reload=False  # 🚀 [性能优化] 不强制重载，允许使用缓存
            )
            
            if response.success:
                # 构建元数据
                metadata = {
                    'table_name': self.table_name,
                    'page': self.page,
                    'page_size': self.page_size,
                    'sort_columns': self.sort_columns,
                    'total_records': getattr(response, 'total_records', 0),
                    'processing_time_ms': (time.time() - start_time) * 1000
                }
                
                # 发射数据就绪信号
                self.data_ready.emit(self.request_id, response.data, metadata)
                
                self.logger.info(f"🔄 [线程成功] 数据加载成功: {self.request_id}, "
                               f"耗时: {metadata['processing_time_ms']:.1f}ms")
            else:
                error_msg = getattr(response, 'error', '未知错误')
                self.loading_error.emit(self.request_id, error_msg)
                
        except Exception as e:
            self.loading_error.emit(self.request_id, str(e))
            self.logger.error(f"🔄 [线程异常] 数据加载异常: {self.request_id} - {e}")


class PerformanceOptimizedSortManager(QObject):
    """🎯 [排序优化] 性能优化的排序管理器"""
    
    sort_completed = pyqtSignal(list)  # 排序完成信号
    
    def __init__(self):
        super().__init__()
        self.logger = setup_logger(__name__ + ".PerformanceOptimizedSortManager")
        
        # 🚀 [排序缓存] 排序结果缓存
        self._sort_cache = {}
        self._cache_timestamps = {}
        self._cache_ttl = 600  # 10分钟TTL，排序结果相对稳定
        
        self.logger.info("🎯 [排序优化] 性能优化排序管理器初始化完成")

    def _generate_sort_cache_key(self, table_name: str, sort_columns: List[Dict]) -> str:
        """🚀 [排序缓存] 生成排序缓存键"""
        sort_parts = []
        for col in sort_columns:
            col_name = col.get('column_name', '')
            order = col.get('order', 'asc')
            sort_parts.append(f"{col_name}:{order}")
        sort_key = "|".join(sort_parts)
        return f"sort_{table_name}_{sort_key}"

    def get_cached_sort_result(self, table_name: str, sort_columns: List[Dict]) -> Optional[Any]:
        """🚀 [排序缓存] 获取缓存的排序结果"""
        if not sort_columns:
            return None
            
        cache_key = self._generate_sort_cache_key(table_name, sort_columns)
        
        if cache_key in self._sort_cache:
            timestamp = self._cache_timestamps.get(cache_key, 0)
            if (time.time() - timestamp) < self._cache_ttl:
                self.logger.info(f"🚀 [排序缓存命中] {table_name}")
                return self._sort_cache[cache_key]
            else:
                # 缓存过期，清理
                self._sort_cache.pop(cache_key, None)
                self._cache_timestamps.pop(cache_key, None)
        
        return None

    def cache_sort_result(self, table_name: str, sort_columns: List[Dict], result: Any):
        """🚀 [排序缓存] 缓存排序结果"""
        if not sort_columns:
            return
            
        cache_key = self._generate_sort_cache_key(table_name, sort_columns)
        self._sort_cache[cache_key] = result
        self._cache_timestamps[cache_key] = time.time()
        
        self.logger.info(f"🚀 [排序缓存存储] {table_name}")


# 🎯 [使用示例] 性能优化集成示例
class IntegratedPerformanceOptimizer:
    """🚀 [综合优化] 集成性能优化器"""
    
    def __init__(self):
        self.data_loader = PerformanceOptimizedDataLoader()
        self.sort_manager = PerformanceOptimizedSortManager()
        self.logger = setup_logger(__name__ + ".IntegratedPerformanceOptimizer")
        
        # 连接信号
        self.data_loader.data_loaded.connect(self._on_data_loaded)
        self.data_loader.loading_started.connect(self._on_loading_started)
        self.data_loader.loading_finished.connect(self._on_loading_finished)
        
        self.logger.info("🚀 [综合优化] 集成性能优化器初始化完成")

    def load_page_data(self, table_name: str, page: int, page_size: int = 50,
                      sort_columns: Optional[List[Dict]] = None):
        """🚀 [综合优化] 优化的分页数据加载"""
        # 🎯 [排序优化] 先检查排序缓存
        if sort_columns:
            cached_sort_result = self.sort_manager.get_cached_sort_result(table_name, sort_columns)
            if cached_sort_result is not None:
                self.logger.info("🎯 [排序缓存命中] 使用缓存的排序结果")
                # 这里可以直接使用缓存的排序结果
                return cached_sort_result
        
        # 🔄 [异步加载] 异步加载数据
        self.data_loader.load_data_async(table_name, page, page_size, sort_columns)

    @pyqtSlot(object, dict)
    def _on_data_loaded(self, data, metadata):
        """🚀 [数据处理] 数据加载完成处理"""
        try:
            table_name = metadata.get('table_name')
            sort_columns = metadata.get('sort_columns')
            from_cache = metadata.get('from_cache', False)
            processing_time = metadata.get('processing_time_ms', 0)
            
            cache_status = "缓存" if from_cache else "数据库"
            self.logger.info(f"🚀 [数据完成] {table_name} 数据加载完成 "
                           f"({cache_status}), 耗时: {processing_time:.1f}ms")
            
            # 🎯 [排序缓存] 如果有排序，缓存排序结果
            if sort_columns and not from_cache:
                self.sort_manager.cache_sort_result(table_name, sort_columns, data)
            
        except Exception as e:
            self.logger.error(f"🚀 [数据处理异常] {e}")

    @pyqtSlot()
    def _on_loading_started(self):
        """🔄 [加载状态] 加载开始"""
        self.logger.info("🔄 [加载开始] 数据加载已开始...")

    @pyqtSlot()
    def _on_loading_finished(self):
        """🔄 [加载状态] 加载完成"""
        self.logger.info("🔄 [加载完成] 所有数据加载已完成")


if __name__ == "__main__":
    """🧪 [测试] 性能优化器测试"""
    
    # 创建性能优化器实例
    optimizer = IntegratedPerformanceOptimizer()
    
    # 测试分页加载
    print("🧪 [测试] 开始测试分页加载...")
    optimizer.load_page_data("test_table", 1, 50)
    
    # 测试排序加载
    print("🧪 [测试] 开始测试排序加载...")
    sort_columns = [{'column_name': 'name', 'order': 'asc'}]
    optimizer.load_page_data("test_table", 1, 50, sort_columns)
    
    print("�� [测试] 性能优化器测试完成") 