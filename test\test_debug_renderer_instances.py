#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试渲染器实例差异
"""

import pandas as pd
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

def test_debug_renderer_instances():
    """调试渲染器实例差异"""
    print("🔍 [实例调试] 开始调试渲染器实例差异...")
    print("=" * 60)
    
    try:
        # 1. 创建测试数据
        test_data = pd.DataFrame({
            '备注': [None, '', '测试备注']
        })
        
        print("📊 [测试数据]:")
        print(test_data)
        print()
        
        # 2. 创建独立的FormatRenderer
        from src.modules.format_management.field_registry import FieldRegistry
        from src.modules.format_management.format_config import FormatConfig
        from src.modules.format_management.format_renderer import FormatRenderer
        
        field_registry1 = FieldRegistry("state/data/field_mappings.json")
        format_config1 = FormatConfig("state/format_config.json")
        renderer1 = FormatRenderer(format_config1, field_registry1)
        
        print("🎨 [独立渲染器] 创建独立FormatRenderer:")
        result1 = renderer1.render_dataframe(test_data, 'retired_employees')
        print(f"  结果: {result1['备注'].tolist()}")
        print(f"  实例ID: {id(renderer1)}")
        print(f"  配置ID: {id(format_config1)}")
        print(f"  注册表ID: {id(field_registry1)}")
        print()
        
        # 3. 创建UnifiedFormatManager并检查其内部渲染器
        from src.modules.format_management.unified_format_manager import UnifiedFormatManager
        format_manager = UnifiedFormatManager()
        
        print("🎨 [统一管理器] 检查UnifiedFormatManager内部渲染器:")
        print(f"  渲染器实例ID: {id(format_manager.format_renderer)}")
        print(f"  配置实例ID: {id(format_manager.format_config)}")
        print(f"  注册表实例ID: {id(format_manager.field_registry)}")
        print()
        
        # 4. 直接调用UnifiedFormatManager的内部渲染器
        print("🎨 [内部渲染器] 直接调用UnifiedFormatManager的内部渲染器:")
        result2 = format_manager.format_renderer.render_dataframe(test_data, 'retired_employees')
        print(f"  结果: {result2['备注'].tolist()}")
        print()
        
        # 5. 调用UnifiedFormatManager的format_data方法
        print("🎨 [统一方法] 调用UnifiedFormatManager.format_data:")
        result3 = format_manager.format_data(test_data, "retired_employees", data_source="test")
        print(f"  结果: {result3['备注'].tolist()}")
        print()
        
        # 6. 比较配置
        print("🔍 [配置比较] 比较两个配置实例:")
        config1 = format_config1.get_format_rules('string', 'retired_employees')
        config2 = format_manager.format_config.get_format_rules('string', 'retired_employees')
        print(f"  独立配置: {config1}")
        print(f"  统一配置: {config2}")
        print(f"  配置相同: {config1 == config2}")
        print()
        
        # 7. 比较字段类型映射
        print("🔍 [映射比较] 比较字段类型映射:")
        types1 = field_registry1.get_table_field_types('retired_employees')
        types2 = format_manager.field_registry.get_table_field_types('retired_employees')
        remarks_type1 = types1.get('备注', 'NOT_FOUND')
        remarks_type2 = types2.get('备注', 'NOT_FOUND')
        print(f"  独立映射备注类型: {remarks_type1}")
        print(f"  统一映射备注类型: {remarks_type2}")
        print(f"  类型相同: {remarks_type1 == remarks_type2}")
        
        return True
        
    except Exception as e:
        print(f"❌ [调试失败] 调试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_debug_renderer_instances()
