#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单比较测试
"""

import pandas as pd
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

def test_simple_comparison():
    """简单比较测试"""
    print("🔍 [简单比较] 开始简单比较测试...")
    print("=" * 60)
    
    try:
        # 1. 创建测试数据
        test_data = pd.DataFrame({
            '备注': [None, '', '测试备注']
        })
        
        print("📊 [原始数据]:")
        print(test_data)
        print(f"  备注列值: {test_data['备注'].tolist()}")
        print(f"  备注列值repr: {[repr(x) for x in test_data['备注'].tolist()]}")
        print()
        
        # 2. 直接使用FormatRenderer
        from src.modules.format_management.field_registry import FieldRegistry
        from src.modules.format_management.format_config import FormatConfig
        from src.modules.format_management.format_renderer import FormatRenderer
        
        field_registry = FieldRegistry("state/data/field_mappings.json")
        format_config = FormatConfig("state/format_config.json")
        renderer = FormatRenderer(format_config, field_registry)
        
        direct_result = renderer.render_dataframe(test_data, 'retired_employees')
        print("📊 [FormatRenderer直接结果]:")
        print(direct_result)
        print(f"  备注列值: {direct_result['备注'].tolist()}")
        print(f"  备注列值repr: {[repr(x) for x in direct_result['备注'].tolist()]}")
        print()
        
        # 3. 使用UnifiedFormatManager
        from src.modules.format_management.unified_format_manager import UnifiedFormatManager
        format_manager = UnifiedFormatManager()
        
        unified_result = format_manager.format_data(test_data, "retired_employees", data_source="test")
        print("📊 [UnifiedFormatManager结果]:")
        print(unified_result)
        print(f"  备注列值: {unified_result['备注'].tolist()}")
        print(f"  备注列值repr: {[repr(x) for x in unified_result['备注'].tolist()]}")
        print()
        
        # 4. 比较两个结果
        print("🔍 [比较结果]:")
        direct_values = direct_result['备注'].tolist()
        unified_values = unified_result['备注'].tolist()
        
        for i, (direct, unified) in enumerate(zip(direct_values, unified_values)):
            if direct != unified:
                print(f"  行{i}: 直接结果='{direct}' vs 统一结果='{unified}' -> 不一致!")
            else:
                print(f"  行{i}: 直接结果='{direct}' vs 统一结果='{unified}' -> 一致")
        
        # 5. 检查DataFrame是否相同
        are_equal = direct_result.equals(unified_result)
        print(f"\n📊 [DataFrame比较] 两个DataFrame是否相同: {are_equal}")
        
        if not are_equal:
            print("🔍 [差异分析] DataFrame不同，分析差异:")
            for col in direct_result.columns:
                if not direct_result[col].equals(unified_result[col]):
                    print(f"  列'{col}'不同:")
                    print(f"    直接结果: {direct_result[col].tolist()}")
                    print(f"    统一结果: {unified_result[col].tolist()}")
        
        return True
        
    except Exception as e:
        print(f"❌ [测试失败] 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_simple_comparison()
