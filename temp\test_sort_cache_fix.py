#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
排序缓存修复验证测试

验证格式化器缓存污染问题是否已修复：
1. 测试格式化器缓存键是否包含数据内容
2. 测试排序后的数据是否能正确格式化
3. 测试缓存是否正确区分不同内容的数据
4. 验证实际排序功能

作者: 薪资系统排序修复团队
创建时间: 2025-07-22
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_cache_key_generation():
    """测试缓存键生成是否包含数据内容"""
    print("🔍 测试1：缓存键生成修复验证")
    
    try:
        from src.modules.format_management.master_format_manager import MasterFormatManager
        import pandas as pd
        
        manager = MasterFormatManager()
        
        # 创建两个相同结构但不同内容的数据
        data1 = [
            {'工号': '19990089', '2025年薪级工资': 2375.0},
            {'工号': '19990090', '2025年薪级工资': 2400.0}
        ]
        
        data2 = [
            {'工号': '19961347', '2025年薪级工资': 0.0},
            {'工号': '20251003', '2025年薪级工资': 0.0}
        ]
        
        # 生成缓存键
        key1 = manager._generate_cache_key(data1, 'active_employees')
        key2 = manager._generate_cache_key(data2, 'active_employees')
        
        print(f"   原始数据缓存键: {key1}")
        print(f"   排序数据缓存键: {key2}")
        
        if key1 != key2:
            print("   ✅ 缓存键包含数据内容，能区分不同数据")
            return True
        else:
            print("   ❌ 缓存键相同，仍然只基于结构")
            return False
            
    except Exception as e:
        print(f"   ❌ 缓存键测试失败: {e}")
        return False

def test_format_cache_isolation():
    """测试格式化缓存隔离"""
    print("\n🔍 测试2：格式化缓存隔离验证")
    
    try:
        from src.modules.format_management.master_format_manager import MasterFormatManager
        import pandas as pd
        
        manager = MasterFormatManager()
        
        # 第一次格式化（模拟原始数据）
        original_data = pd.DataFrame([
            {'工号': '19990089', '2025年薪级工资': 2375.0},
            {'工号': '19990090', '2025年薪级工资': 2400.0}
        ])
        
        result1 = manager.format_table_data(original_data, 'active_employees')
        
        # 第二次格式化（模拟排序后数据）
        sorted_data = pd.DataFrame([
            {'工号': '19961347', '2025年薪级工资': 0.0},
            {'工号': '20251003', '2025年薪级工资': 0.0}
        ])
        
        result2 = manager.format_table_data(sorted_data, 'active_employees')
        
        # 验证结果是否不同
        if not result1.equals(result2):
            print("   ✅ 格式化缓存正确隔离，不同数据返回不同结果")
            print(f"   第一次结果第一行工号: {result1.iloc[0]['工号'] if '工号' in result1.columns else 'N/A'}")
            print(f"   第二次结果第一行工号: {result2.iloc[0]['工号'] if '工号' in result2.columns else 'N/A'}")
            return True
        else:
            print("   ❌ 格式化缓存污染，返回了相同结果")
            return False
            
    except Exception as e:
        print(f"   ❌ 格式化缓存测试失败: {e}")
        return False

def test_sort_data_verification():
    """测试排序数据验证逻辑"""
    print("\n🔍 测试3：排序数据验证逻辑")
    
    try:
        # 检查VirtualizedExpandableTable中的数据验证代码
        with open('src/gui/prototype/widgets/virtualized_expandable_table.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            ('🚨 [排序修复] 验证格式化后的数据顺序是否正确', '数据顺序验证'),
            ('格式化破坏了数据顺序', '数据顺序检查'),
            ('跳过格式化，使用原始排序数据', '排序数据保护'),
            ('格式化完成，数据顺序正确', '验证成功确认'),
        ]
        
        all_present = True
        for check_text, description in checks:
            if check_text in content:
                print(f"   ✅ {description}: 已实现")
            else:
                print(f"   ❌ {description}: 缺失")
                all_present = False
        
        return all_present
        
    except Exception as e:
        print(f"   ❌ 数据验证逻辑检查失败: {e}")
        return False

def test_architecture_fixes_summary():
    """测试架构修复总结"""
    print("\n🎯 架构修复总结验证")
    
    checks = [
        test_cache_key_generation(),
        test_format_cache_isolation(),
        test_sort_data_verification()
    ]
    
    passed = sum(checks)
    total = len(checks)
    
    print(f"\n📊 测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("\n🎉 所有架构修复验证通过！")
        print("\n📋 修复摘要:")
        print("  ✅ 格式化器缓存键现在包含数据内容")
        print("  ✅ 排序后的数据不会被缓存污染")
        print("  ✅ 数据顺序验证逻辑已实现")
        print("  ✅ 缓存隔离机制正常工作")
        
        print("\n🔧 建议下一步:")
        print("  1. 启动主程序测试排序功能")
        print("  2. 验证点击表头排序是否正常")
        print("  3. 检查日志确认修复生效")
        
        return True
    else:
        print(f"\n❌ 仍有 {total - passed} 个问题需要解决")
        return False

if __name__ == "__main__":
    print("🚨 排序缓存修复验证测试开始")
    print("=" * 60)
    
    success = test_architecture_fixes_summary()
    
    if success:
        print("\n✅ 排序功能修复完成！现在可以测试实际排序功能。")
    else:
        print("\n❌ 排序功能修复验证失败，需要进一步调试。") 