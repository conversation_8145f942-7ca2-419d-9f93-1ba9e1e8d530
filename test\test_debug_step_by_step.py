#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
逐步调试格式化过程
"""

import pandas as pd
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

def test_debug_step_by_step():
    """逐步调试格式化过程"""
    print("🔍 [逐步调试] 开始逐步调试格式化过程...")
    print("=" * 60)
    
    try:
        # 1. 创建测试数据
        test_data = pd.DataFrame({
            '备注': [None, '', '测试备注']
        })
        
        print("📊 [步骤1] 原始数据:")
        print(test_data)
        print(f"  数据类型: {test_data['备注'].dtype}")
        print(f"  空值检查: {test_data['备注'].isna().tolist()}")
        print()
        
        # 2. 初始化组件
        from src.modules.format_management.field_registry import FieldRegistry
        from src.modules.format_management.format_config import FormatConfig
        from src.modules.format_management.format_renderer import FormatRenderer
        
        field_registry = FieldRegistry("state/data/field_mappings.json")
        format_config = FormatConfig("state/format_config.json")
        renderer = FormatRenderer(format_config, field_registry)
        
        # 3. 检查字段类型
        field_types = field_registry.get_table_field_types("retired_employees")
        remarks_type = field_types.get('备注', 'NOT_FOUND')
        print(f"📋 [步骤2] 字段类型: {remarks_type}")
        
        # 4. 检查格式配置
        string_config = format_config.get_format_rules('string', 'retired_employees')
        print(f"📋 [步骤3] 格式配置: {string_config}")
        print()
        
        # 5. 逐步调用格式化方法
        print("🎨 [步骤4] 调用render_column方法:")
        formatted_column = renderer.render_column(test_data['备注'], 'string', '备注', 'retired_employees')
        print(f"  结果: {formatted_column.tolist()}")
        print(f"  数据类型: {formatted_column.dtype}")
        print()
        
        # 6. 创建完整DataFrame并调用render_dataframe
        print("🎨 [步骤5] 调用render_dataframe方法:")
        formatted_df = renderer.render_dataframe(test_data, 'retired_employees')
        print(f"  结果: {formatted_df['备注'].tolist()}")
        print(f"  数据类型: {formatted_df['备注'].dtype}")
        print()
        
        # 7. 检查是否有后处理逻辑
        print("🔍 [步骤6] 检查DataFrame后处理:")
        print("  原始DataFrame:")
        print(f"    {test_data['备注'].tolist()}")
        print("  render_dataframe结果:")
        print(f"    {formatted_df['备注'].tolist()}")
        
        # 8. 检查是否有fillna操作
        print("\n🔍 [步骤7] 检查是否有fillna操作:")
        # 手动检查是否有地方调用了fillna
        test_fillna = formatted_df.copy()
        test_fillna = test_fillna.fillna('-')
        print(f"  如果调用fillna('-'): {test_fillna['备注'].tolist()}")
        
        # 9. 使用UnifiedFormatManager
        print("\n🎨 [步骤8] 使用UnifiedFormatManager:")
        from src.modules.format_management.unified_format_manager import UnifiedFormatManager
        format_manager = UnifiedFormatManager()
        
        final_result = format_manager.format_data(test_data, "retired_employees", data_source="test")
        print(f"  最终结果: {final_result['备注'].tolist()}")
        print(f"  数据类型: {final_result['备注'].dtype}")
        
        return True
        
    except Exception as e:
        print(f"❌ [调试失败] 调试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_debug_step_by_step()
