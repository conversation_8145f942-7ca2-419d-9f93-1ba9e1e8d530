#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import pandas as pd
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_master_format_manager():
    """测试主格式化管理器"""
    try:
        print("=== 测试主格式化管理器 ===")
        
        # 创建测试数据（模拟离休人员表的原始数据）
        test_data = pd.DataFrame([
            {
                'employee_id': '19289006.0',
                'employee_name': '王太西',
                'one_time_living_allowance': None,
                'supplement': None,
                'advance': None,
                'month': '2025-07',
                'year': '2025',
                'remarks': None
            },
            {
                'employee_id': '19339009.0',
                'employee_name': '赵君励',
                'one_time_living_allowance': None,
                'supplement': None,
                'advance': None,
                'month': '2025-07',
                'year': '2025',
                'remarks': None
            }
        ])
        
        print('原始数据:')
        for i, (idx, row) in enumerate(test_data.iterrows()):
            print(f'  第{i}行:')
            print(f'    employee_id: {repr(row["employee_id"])}')
            print(f'    one_time_living_allowance: {repr(row["one_time_living_allowance"])}')
            print(f'    month: {repr(row["month"])}')
            print(f'    year: {repr(row["year"])}')
            print(f'    remarks: {repr(row["remarks"])}')
        print()
        
        # 导入主格式化管理器
        from src.modules.format_management.master_format_manager import get_master_format_manager
        
        # 获取主格式化管理器
        master_formatter = get_master_format_manager()
        
        print('✅ 主格式化管理器获取成功')
        
        # 测试表格数据格式化
        formatted_df = master_formatter.format_table_data(test_data, "retired_employees")
        
        print('格式化后数据:')
        for i, (idx, row) in enumerate(formatted_df.iterrows()):
            print(f'  第{i}行:')
            print(f'    employee_id: {repr(row["employee_id"])}')
            print(f'    one_time_living_allowance: {repr(row["one_time_living_allowance"])}')
            print(f'    month: {repr(row["month"])}')
            print(f'    year: {repr(row["year"])}')
            print(f'    remarks: {repr(row["remarks"])}')
        print()
        
        # 测试单个字段格式化
        print('=== 测试单个字段格式化 ===')
        
        # 测试人员代码
        employee_id_result = master_formatter.format_display_value('19289006.0', 'employee_id')
        print(f'人员代码格式化: "19289006.0" → "{employee_id_result}"')
        
        # 测试增发一次性生活补贴
        allowance_result = master_formatter.format_display_value(None, '增发一次性生活补贴')
        print(f'增发一次性生活补贴格式化: None → "{allowance_result}"')
        
        # 测试月份
        month_result = master_formatter.format_display_value('2025-07', 'month')
        print(f'月份格式化: "2025-07" → "{month_result}"')
        
        # 测试年份
        year_result = master_formatter.format_display_value('2025', 'year')
        print(f'年份格式化: "2025" → "{year_result}"')
        
        # 测试备注
        remarks_result = master_formatter.format_display_value(None, 'remarks')
        print(f'备注格式化: None → "{remarks_result}"')
        
        print()
        
        # 验证格式化效果
        print('=== 格式化效果验证 ===')
        
        # 检查人员代码（应该移除小数点）
        if employee_id_result == '19289006':
            print('✅ 人员代码格式化正确: 移除了小数点')
        else:
            print(f'❌ 人员代码格式化错误: 期望"19289006"，实际"{employee_id_result}"')
        
        # 检查浮点数字段（应该显示为"0.00"）
        if allowance_result == '0.00':
            print('✅ 浮点数字段格式化正确: 空值显示为"0.00"')
        else:
            print(f'❌ 浮点数字段格式化错误: 期望"0.00"，实际"{allowance_result}"')
        
        # 检查月份字段（应该显示为"07"）
        if month_result == '07':
            print('✅ 月份字段格式化正确: 提取后两位数字')
        else:
            print(f'❌ 月份字段格式化错误: 期望"07"，实际"{month_result}"')
        
        # 检查年份字段（应该显示为"2025"）
        if year_result == '2025':
            print('✅ 年份字段格式化正确: 保持年份格式')
        else:
            print(f'❌ 年份字段格式化错误: 期望"2025"，实际"{year_result}"')
        
        # 检查备注字段（应该显示为空字符串）
        if remarks_result == '':
            print('✅ 备注字段格式化正确: 空值显示为空字符串')
        else:
            print(f'❌ 备注字段格式化错误: 期望""，实际"{remarks_result}"')
        
        print('\n=== 总结 ===')
        print('主格式化管理器测试完成！')
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_master_format_manager()
