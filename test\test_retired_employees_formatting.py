#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
离休人员工资表格式化测试
验证离休人员工资表在各种数据加载路径下的格式化一致性

测试内容：
1. 字段注册配置验证
2. 格式化规则验证
3. 数据类型转换验证
4. 隐藏字段处理验证
5. 空值处理验证

创建时间: 2025-07-31
"""

import sys
import os
from pathlib import Path
import pandas as pd
import json

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_field_registry_config():
    """测试字段注册配置"""
    print("🧪 测试1: 字段注册配置验证")
    
    try:
        from src.modules.format_management.field_registry import FieldRegistry
        
        # 创建字段注册实例
        registry_path = project_root / "state" / "data" / "field_mappings.json"
        field_registry = FieldRegistry(str(registry_path))
        
        # 加载映射
        field_registry.load_mappings()
        
        # 测试离休人员表配置
        table_type = "retired_employees"
        
        # 检查字段映射
        field_mappings = field_registry.get_table_mapping(table_type)
        print(f"  ✅ 字段映射数量: {len(field_mappings)}")

        # 检查字段类型
        field_types = field_registry.get_table_field_types(table_type)
        print(f"  ✅ 字段类型数量: {len(field_types)}")
        
        # 验证浮点型字段
        expected_float_fields = [
            "basic_retirement_salary", "balance_allowance", "living_allowance",
            "housing_allowance", "property_allowance", "retirement_allowance",
            "nursing_fee", "one_time_living_allowance", "supplement", "total", "advance"
        ]
        
        float_fields_found = 0
        for field in expected_float_fields:
            if field in field_types and field_types[field] == "float":
                float_fields_found += 1
        
        print(f"  ✅ 浮点型字段配置: {float_fields_found}/{len(expected_float_fields)}")
        
        # 验证特殊字段
        month_field_type = field_types.get("month", "")
        year_field_type = field_types.get("year", "")
        print(f"  ✅ 月份字段类型: {month_field_type}")
        print(f"  ✅ 年份字段类型: {year_field_type}")
        
        # 检查隐藏字段
        hidden_fields = field_registry.get_hidden_fields(table_type)
        print(f"  ✅ 隐藏字段: {hidden_fields}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 字段注册配置测试失败: {e}")
        return False

def test_format_config():
    """测试格式配置文件"""
    print("\n🧪 测试2: 格式配置文件验证")
    
    try:
        config_path = project_root / "state" / "format_config.json"
        
        if not config_path.exists():
            print(f"  ❌ 配置文件不存在: {config_path}")
            return False
        
        with open(config_path, 'r', encoding='utf-8') as f:
            config = json.load(f)
        
        # 检查默认格式配置
        default_formats = config.get("default_formats", {})
        
        # 验证浮点型格式
        float_config = default_formats.get("float", {})
        decimal_places = float_config.get("decimal_places", 0)
        zero_display = float_config.get("zero_display", "")
        thousand_separator = float_config.get("thousand_separator", ",")
        
        print(f"  ✅ 浮点型小数位数: {decimal_places}")
        print(f"  ✅ 浮点型零值显示: '{zero_display}'")
        print(f"  ✅ 浮点型千分位分隔符: '{thousand_separator}'")
        
        # 验证字符串格式
        string_config = default_formats.get("string", {})
        empty_display = string_config.get("empty_display", "-")
        print(f"  ✅ 字符串空值显示: '{empty_display}'")
        
        # 验证特殊字段格式
        month_config = default_formats.get("month_string", {})
        year_config = default_formats.get("year_string", {})
        print(f"  ✅ 月份字符串配置: {month_config}")
        print(f"  ✅ 年份字符串配置: {year_config}")
        
        # 检查离休人员专门配置
        retired_config = config.get("retired_employees_format_config", {})
        if retired_config:
            print(f"  ✅ 离休人员专门配置存在")
            float_fields = retired_config.get("field_formats", {}).get("float_fields", [])
            print(f"  ✅ 离休人员浮点型字段数量: {len(float_fields)}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 格式配置文件测试失败: {e}")
        return False

def test_data_formatting():
    """测试数据格式化功能"""
    print("\n🧪 测试3: 数据格式化功能验证")
    
    try:
        # 创建测试数据
        test_data = {
            "employee_id": ["001", "002", "003", "004"],
            "employee_name": ["张三", "李四", None, ""],
            "department": ["财务部", None, "人事部", ""],
            "remarks": ["正常", "", None, "特殊情况"],
            "basic_retirement_salary": [3500.123, 4200.0, None, ""],
            "balance_allowance": [500.50, 600, 0, "700.25"],
            "living_allowance": [200.00, None, 150.75, ""],
            "housing_allowance": [1200.999, 0.0, "invalid", 1500],
            "year": [2025, "2025", None, ""],
            "month": ["202501", "01", "12", "202512"],
            "created_at": ["2025-01-01", "2025-01-02", "2025-01-03", "2025-01-04"],
            "id": [1, 2, 3, 4]
        }
        
        df = pd.DataFrame(test_data)
        print(f"  ✅ 测试数据创建成功: {len(df)} 行, {len(df.columns)} 列")
        
        # 测试格式化渲染器
        from src.modules.format_management.format_renderer import FormatRenderer
        from src.modules.format_management.field_registry import FieldRegistry
        from src.modules.format_management.format_config import FormatConfig

        registry_path = project_root / "state" / "data" / "field_mappings.json"
        field_registry = FieldRegistry(str(registry_path))
        field_registry.load_mappings()

        config_path = project_root / "state" / "format_config.json"
        format_config = FormatConfig(str(config_path))
        format_config.load_config()

        renderer = FormatRenderer(format_config, field_registry)
        
        # 测试浮点型字段格式化
        print("\n  📊 浮点型字段格式化测试:")
        float_fields = ["basic_retirement_salary", "balance_allowance", "living_allowance"]
        
        for field in float_fields:
            if field in df.columns:
                format_config = {"decimal_places": 2, "zero_display": "0.00", "thousand_separator": ""}
                formatted_column = renderer._render_float_column(df[field], format_config, field)
                print(f"    {field}:")
                for i, (original, formatted) in enumerate(zip(df[field], formatted_column)):
                    print(f"      行{i}: {original} -> '{formatted}'")
        
        # 测试字符串字段格式化
        print("\n  📊 字符串字段格式化测试:")
        string_fields = ["employee_name", "department", "remarks"]
        
        for field in string_fields:
            if field in df.columns:
                format_config = {"empty_display": "", "trim_whitespace": True}
                formatted_column = renderer._render_string_column(df[field], format_config, field)
                print(f"    {field}:")
                for i, (original, formatted) in enumerate(zip(df[field], formatted_column)):
                    print(f"      行{i}: {original} -> '{formatted}'")
        
        # 测试特殊字段格式化
        print("\n  📊 特殊字段格式化测试:")
        
        # 月份字段
        if "month" in df.columns:
            format_config = {"extract_last_two": True}
            formatted_month = renderer._render_month_string_column(df["month"], format_config, "month")
            print(f"    月份字段:")
            for i, (original, formatted) in enumerate(zip(df["month"], formatted_month)):
                print(f"      行{i}: {original} -> '{formatted}'")
        
        # 年份字段
        if "year" in df.columns:
            format_config = {"convert_to_string": True}
            formatted_year = renderer._render_year_string_column(df["year"], format_config, "year")
            print(f"    年份字段:")
            for i, (original, formatted) in enumerate(zip(df["year"], formatted_year)):
                print(f"      行{i}: {original} -> '{formatted}'")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 数据格式化功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主测试函数"""
    print("🔧 离休人员工资表格式化测试")
    print("=" * 50)
    
    tests = [
        test_field_registry_config,
        test_format_config,
        test_data_formatting
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"  ❌ 测试执行失败: {e}")
    
    print("\n📊 测试总结")
    print("=" * 30)
    success_rate = passed / total * 100
    
    if passed == total:
        print(f"🎉 所有测试通过！({passed}/{total}, {success_rate:.1f}%)")
        print("✅ 离休人员工资表格式化配置成功")
        return True
    else:
        print(f"⚠️ 部分测试失败 ({passed}/{total}, {success_rate:.1f}%)")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
