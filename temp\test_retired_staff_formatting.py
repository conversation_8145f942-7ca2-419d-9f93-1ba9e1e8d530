#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
离休人员表格式化功能测试

测试离休人员表的专用格式化功能是否正常工作。

创建时间: 2025-07-25
"""

import sys
import os
import pandas as pd
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_format_config():
    """测试格式化配置"""
    print("🔧 [测试] 测试格式化配置...")
    
    try:
        from src.modules.format_management.format_config import FormatConfig
        
        # 创建配置实例
        config_path = "temp/test_format_config.json"
        format_config = FormatConfig(config_path)
        
        # 加载配置
        format_config.load_config()
        
        # 测试获取离休人员表配置
        retired_config = format_config.get_retired_staff_format_config()
        print(f"✅ 离休人员表配置: {retired_config}")
        
        # 测试表类型判断
        test_names = [
            "离休人员",
            "离休人员工资表", 
            "2025年5月离休人员",
            "全部在职人员",
            "A岗职工表"
        ]
        
        for name in test_names:
            is_retired = format_config.is_retired_staff_table(name)
            print(f"表名: '{name}' -> 是否为离休人员表: {is_retired}")
            
        return True
        
    except Exception as e:
        print(f"❌ 格式化配置测试失败: {e}")
        return False

def test_table_formatting():
    """测试表格格式化功能"""
    print("\n🔧 [测试] 测试表格格式化功能...")
    
    try:
        # 创建测试数据
        test_data = {
            "姓名": ["张三", "李四", None, ""],
            "部门名称": ["财务部", None, "人事部", ""],
            "备注": ["正常", "", None, "特殊情况"],
            "基本离休费": [3500.123, 4200.0, None, ""],
            "结余津贴": [500.50, 600, 0, "700.25"],
            "生活补贴": [200.00, None, 150.75, ""]
        }
        
        df = pd.DataFrame(test_data)
        print(f"✅ 测试数据创建成功: {len(df)} 行, {len(df.columns)} 列")
        print(f"数据预览:\n{df}")
        
        # 模拟格式化测试
        print("\n🔧 [测试] 模拟格式化结果:")
        
        # 浮点数字段测试
        float_fields = ["基本离休费", "结余津贴", "生活补贴"]
        string_fields = ["姓名", "部门名称", "备注"]
        
        for col in df.columns:
            print(f"\n字段: {col}")
            for idx, value in enumerate(df[col]):
                if col in float_fields:
                    # 模拟货币格式化
                    if pd.isna(value) or value is None or value == '':
                        formatted = "-"
                    else:
                        try:
                            float_val = float(value)
                            formatted = f"{float_val:.2f}"
                        except (ValueError, TypeError):
                            formatted = str(value) if value is not None else "-"
                elif col in string_fields:
                    # 模拟字符串格式化
                    if pd.isna(value) or value is None:
                        formatted = ""
                    else:
                        result = str(value).strip()
                        if result.lower() in ['nan', 'none', 'null']:
                            formatted = ""
                        else:
                            formatted = result
                else:
                    formatted = str(value) if value is not None else ""
                
                print(f"  行{idx}: {value} -> '{formatted}'")
        
        return True
        
    except Exception as e:
        print(f"❌ 表格格式化测试失败: {e}")
        return False

def test_table_type_recognition():
    """测试表类型识别"""
    print("\n🔧 [测试] 测试表类型识别...")
    
    # 模拟表类型识别逻辑
    def extract_table_type_from_name(table_name: str) -> str:
        """模拟表类型识别"""
        if not table_name:
            return 'active_employees'
        
        table_type_mappings = {
            'default_table': 'active_employees',
            'active_employees': 'active_employees',
            'retired_employees': 'retired_employees', 
            'pension_employees': 'retired_employees',
            '全部在职人员': 'active_employees',
            '离休人员': 'retired_employees',
            '退休人员': 'retired_employees',
            '离休人员工资表': 'retired_employees',
            '离休': 'retired_employees',
            'retired_staff': 'retired_employees'
        }
        
        # 精确匹配
        if table_name in table_type_mappings:
            return table_type_mappings[table_name]
        
        # 模糊匹配
        table_name_lower = table_name.lower()
        for pattern, table_type in table_type_mappings.items():
            if pattern.lower() in table_name_lower:
                return table_type
        
        return 'active_employees'
    
    # 测试用例
    test_cases = [
        ("离休人员", "retired_employees"),
        ("离休人员工资表", "retired_employees"), 
        ("2025年5月离休人员", "retired_employees"),
        ("全部在职人员", "active_employees"),
        ("A岗职工表", "active_employees"),
        ("退休人员", "retired_employees"),
        ("", "active_employees")
    ]
    
    all_passed = True
    for table_name, expected in test_cases:
        result = extract_table_type_from_name(table_name)
        passed = result == expected
        status = "✅" if passed else "❌"
        print(f"{status} 表名: '{table_name}' -> 识别类型: {result} (期望: {expected})")
        if not passed:
            all_passed = False
    
    return all_passed

def main():
    """主测试函数"""
    print("🚀 开始离休人员表格式化功能测试\n")
    
    tests = [
        ("格式化配置测试", test_format_config),
        ("表格格式化测试", test_table_formatting), 
        ("表类型识别测试", test_table_type_recognition)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n{'='*50}")
        print(f"🔧 [测试] {test_name}")
        print('='*50)
        
        try:
            if test_func():
                print(f"✅ {test_name} 通过")
                passed += 1
            else:
                print(f"❌ {test_name} 失败")
        except Exception as e:
            print(f"❌ {test_name} 异常: {e}")
    
    print(f"\n{'='*50}")
    print(f"🏁 测试完成: {passed}/{total} 通过")
    print('='*50)
    
    if passed == total:
        print("🎉 所有测试通过！离休人员表格式化功能实现成功。")
        return True
    else:
        print("⚠️  部分测试失败，请检查实现。")
        return False

if __name__ == "__main__":
    main() 