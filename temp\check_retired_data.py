#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import pandas as pd
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def check_retired_data():
    """检查离休人员表的原始数据"""
    try:
        # 连接数据库
        conn = sqlite3.connect('data/db/salary_system.db')
        
        # 查询离休人员表的原始数据
        query = 'SELECT * FROM salary_data_2025_07_retired_employees LIMIT 5'
        df = pd.read_sql_query(query, conn)
        
        print('=== 离休人员表原始数据 ===')
        print('数据形状:', df.shape)
        print('列名:', list(df.columns))
        print()
        
        print('前2行数据:')
        for i, row in df.iterrows():
            print(f'第{i}行:')
            print(f'  employee_id: {repr(row.get("employee_id", "N/A"))} (类型: {type(row.get("employee_id", "N/A"))})')
            print(f'  employee_name: {repr(row.get("employee_name", "N/A"))}')
            print(f'  one_time_living_allowance: {repr(row.get("one_time_living_allowance", "N/A"))} (类型: {type(row.get("one_time_living_allowance", "N/A"))})')
            print(f'  supplement: {repr(row.get("supplement", "N/A"))} (类型: {type(row.get("supplement", "N/A"))})')
            print(f'  advance: {repr(row.get("advance", "N/A"))} (类型: {type(row.get("advance", "N/A"))})')
            print(f'  month: {repr(row.get("month", "N/A"))} (类型: {type(row.get("month", "N/A"))})')
            print(f'  remarks: {repr(row.get("remarks", "N/A"))} (类型: {type(row.get("remarks", "N/A"))})')
            print()
        
        conn.close()
        
    except Exception as e:
        print(f"错误: {e}")

if __name__ == "__main__":
    check_retired_data()
