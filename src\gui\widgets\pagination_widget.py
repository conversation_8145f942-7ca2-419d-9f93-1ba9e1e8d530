# -*- coding: utf-8 -*-
"""
分页组件模块

提供通用的分页控件，支持页码导航、页面大小控制、快速跳转等功能。
适用于大数据量表格的分页浏览。
"""

import logging
import time
from dataclasses import dataclass, field
from typing import List, Dict, Any, Optional
from PyQt5.QtCore import pyqtSignal, Qt
from PyQt5.QtWidgets import (
    QWidget, QHBoxLayout, QPushButton, QLabel, 
    QLineEdit, QComboBox, QSpacerItem, QSizePolicy, QFrame
)

from src.utils.log_config import setup_logger


@dataclass
class PaginationState:
    """分页状态数据类 - 增强版，支持排序状态管理"""
    current_page: int = 1           # 当前页码
    page_size: int = 50            # 每页大小
    total_records: int = 0         # 总记录数
    total_pages: int = 0           # 总页数
    start_record: int = 0          # 当前页起始记录号
    end_record: int = 0            # 当前页结束记录号
    
    # 🆕 排序状态字段
    sort_columns: List[Dict[str, Any]] = field(default_factory=list)  # 排序列信息
    table_type: str = ""           # 工资表类型标识（A岗职工表、离休人员表等）
    sort_dirty: bool = False       # 排序状态是否发生变化
    last_sort_time: float = 0.0    # 最后排序时间
    session_id: Optional[str] = None  # 数据源会话ID
    
    # 路径状态信息
    data_path_type: str = "pagination"  # 数据路径类型（navigation/pagination）
    field_mapping: Dict[str, str] = field(default_factory=dict)  # 字段映射
    inherited_from_navigation: bool = False  # 是否从导航路径继承状态
    
    def __post_init__(self):
        """计算派生字段"""
        self.calculate_derived_fields()
    
    def calculate_derived_fields(self):
        """计算派生字段：总页数、起始记录号、结束记录号"""
        if self.page_size > 0:
            self.total_pages = max(1, (self.total_records + self.page_size - 1) // self.page_size)
        else:
            self.total_pages = 1
        
        if self.total_records > 0:
            self.start_record = (self.current_page - 1) * self.page_size + 1
            self.end_record = min(self.current_page * self.page_size, self.total_records)
        else:
            self.start_record = 0
            self.end_record = 0
    
    def is_valid(self) -> bool:
        """检查分页状态是否有效"""
        return (
            self.current_page >= 1 and 
            self.current_page <= self.total_pages and
            self.page_size > 0 and
            self.total_records >= 0
        )
    
    # 🆕 排序状态管理方法
    def update_sort_state(self, sort_columns: List[Dict[str, Any]]):
        """更新排序状态"""
        if sort_columns != self.sort_columns:
            self.sort_columns = sort_columns.copy() if sort_columns else []
            self.sort_dirty = True
            self.last_sort_time = time.time()
    
    def clear_sort_state(self):
        """清除排序状态"""
        if self.sort_columns:
            self.sort_columns.clear()
            self.sort_dirty = True
            self.last_sort_time = time.time()
    
    def has_sort_state(self) -> bool:
        """检查是否有排序状态"""
        return bool(self.sort_columns)
    
    def get_sort_description(self) -> str:
        """获取排序描述"""
        if not self.sort_columns:
            return "无排序"
        
        descriptions = []
        for i, sort_col in enumerate(self.sort_columns):
            col_name = sort_col.get('column_name', f'列{sort_col.get("column_index", i)}')
            order_text = "升序" if sort_col.get('order') == 'ascending' else "降序"
            priority_text = f"({i+1})" if len(self.sort_columns) > 1 else ""
            descriptions.append(f"{col_name}{priority_text}: {order_text}")
        
        return "; ".join(descriptions)
    
    def copy_sort_state_from(self, other_state: 'PaginationState'):
        """从另一个分页状态复制排序状态"""
        if other_state and other_state.sort_columns:
            self.sort_columns = other_state.sort_columns.copy()
            self.last_sort_time = other_state.last_sort_time
            self.sort_dirty = True
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'current_page': self.current_page,
            'page_size': self.page_size,
            'total_records': self.total_records,
            'total_pages': self.total_pages,
            'start_record': self.start_record,
            'end_record': self.end_record,
            'sort_columns': self.sort_columns,
            'table_type': self.table_type,
            'sort_dirty': self.sort_dirty,
            'last_sort_time': self.last_sort_time,
            'session_id': self.session_id,
            'data_path_type': self.data_path_type,
            'field_mapping': self.field_mapping,
            'inherited_from_navigation': self.inherited_from_navigation
        }


class PaginationWidget(QWidget):
    """分页控件组件"""
    
    # 信号定义
    page_changed = pyqtSignal(int)
    page_size_changed = pyqtSignal(int)
    goto_page_requested = pyqtSignal(int)
    refresh_requested = pyqtSignal()
    
    # 🆕 排序状态相关信号
    sort_state_changed = pyqtSignal(list)  # 排序状态变化信号
    navigation_to_pagination_sync = pyqtSignal(dict)  # 导航到分页同步信号
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.logger = setup_logger(__name__)
        self.state = PaginationState()
        self.page_size_options = [20, 50, 100, 200]
        self.default_page_size = 50
        
        # 🔧 [静默模式修复] 添加静默更新标志，防止信号循环
        self._silent_update_mode = False
        
        # UI组件
        self.first_page_btn = None
        self.prev_page_btn = None
        self.next_page_btn = None
        self.last_page_btn = None
        self.page_input = None
        self.page_size_combo = None
        self.info_label = None
        self.jump_btn = None
        self.refresh_btn = None
        self.total_pages_label = None
        
        self._setup_ui()
        self._apply_styles()  # 应用Material Design样式
        self._connect_signals()
        
        # 🚀 [智能防抖v2.0] 使用智能防抖系统替代固定延迟
        try:
            from src.core.smart_debounce import get_smart_debounce_manager
            self.smart_debounce = get_smart_debounce_manager()
            self.logger.info("✅ [防抖升级] 智能防抖系统已启用")
        except ImportError:
            # 回退到原有机制
            self._last_page_change_time = 0
            self._debounce_delay = 0.02  # 降低到20ms
            self.smart_debounce = None
            self.logger.warning("⚠️ [防抖回退] 使用固定20ms防抖")

        self.logger.info("分页组件初始化完成")
    
    def _setup_ui(self):
        """设置现代化UI界面 - Material Design 3.0"""
        # 主容器样式
        self.setStyleSheet("""
            QWidget {
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f8f9fa);
                border: 1px solid #e8f0fe;
                border-radius: 12px;
                padding: 8px;
            }
        """)

        # 主布局
        main_layout = QHBoxLayout(self)
        main_layout.setContentsMargins(16, 12, 16, 12)
        main_layout.setSpacing(16)
        
        # 左侧：导航按钮组 - 现代化设计
        nav_frame = QFrame()
        nav_frame.setStyleSheet("""
            QFrame {
                background: rgba(66, 133, 244, 0.05);
                border: 1px solid #e8f0fe;
                border-radius: 10px;
                padding: 4px;
            }
        """)
        nav_layout = QHBoxLayout(nav_frame)
        nav_layout.setContentsMargins(12, 10, 12, 10)
        nav_layout.setSpacing(8)

        # 创建现代化导航按钮
        self.first_page_btn = QPushButton("⏮️ 首页")
        self.first_page_btn.setFixedSize(80, 40)
        self.first_page_btn.setToolTip("跳转到首页")
        self.first_page_btn.setCursor(Qt.PointingHandCursor)

        self.prev_page_btn = QPushButton("◀️ 上一页")
        self.prev_page_btn.setFixedSize(100, 40)
        self.prev_page_btn.setToolTip("上一页")
        self.prev_page_btn.setCursor(Qt.PointingHandCursor)

        # 页码输入区域
        page_label = QLabel("页码:")
        page_label.setStyleSheet("""
            QLabel {
                color: #4285f4;
                font-size: 13px;
                font-weight: 600;
                padding: 0 4px;
            }
        """)

        self.page_input = QLineEdit()
        self.page_input.setFixedSize(80, 40)
        self.page_input.setAlignment(Qt.AlignCenter)
        self.page_input.setPlaceholderText("页码")

        self.total_pages_label = QLabel("/ 1页")
        self.total_pages_label.setStyleSheet("""
            QLabel {
                color: #5f6368;
                font-size: 13px;
                font-weight: 500;
                padding: 0 8px;
            }
        """)

        self.jump_btn = QPushButton("🎯 跳转")
        self.jump_btn.setFixedSize(80, 40)
        self.jump_btn.setToolTip("跳转到指定页")
        self.jump_btn.setCursor(Qt.PointingHandCursor)

        self.next_page_btn = QPushButton("下一页 ▶️")
        self.next_page_btn.setFixedSize(100, 40)
        self.next_page_btn.setToolTip("下一页")
        self.next_page_btn.setCursor(Qt.PointingHandCursor)

        self.last_page_btn = QPushButton("末页 ⏭️")
        self.last_page_btn.setFixedSize(80, 40)
        self.last_page_btn.setToolTip("跳转到末页")
        self.last_page_btn.setCursor(Qt.PointingHandCursor)
        
        # 添加到导航布局
        nav_layout.addWidget(self.first_page_btn)
        nav_layout.addWidget(self.prev_page_btn)
        nav_layout.addWidget(page_label)
        nav_layout.addWidget(self.page_input)
        nav_layout.addWidget(self.total_pages_label)
        nav_layout.addWidget(self.jump_btn)
        nav_layout.addWidget(self.next_page_btn)
        nav_layout.addWidget(self.last_page_btn)
        
        # 分隔符
        separator = QFrame()
        separator.setFrameShape(QFrame.VLine)
        separator.setFrameShadow(QFrame.Sunken)
        
        # 右侧：页面大小控制
        size_frame = QFrame()
        size_layout = QHBoxLayout(size_frame)
        size_layout.setContentsMargins(10, 6, 10, 6)
        size_layout.setSpacing(8)
        
        size_label = QLabel("每页:")
        self.page_size_combo = QComboBox()
        self.page_size_combo.setFixedSize(110, 34)
        for size in self.page_size_options:
            self.page_size_combo.addItem(f"{size}条", size)
        self.page_size_combo.setCurrentText(f"{self.default_page_size}条")
        
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.setFixedSize(70, 34)
        
        size_layout.addWidget(size_label)
        size_layout.addWidget(self.page_size_combo)
        size_layout.addWidget(self.refresh_btn)
        
        # 统计信息
        self.info_label = QLabel("共0条记录")
        
        # 弹性空间
        spacer = QSpacerItem(20, 20, QSizePolicy.Expanding, QSizePolicy.Minimum)
        
        # 组装主布局
        main_layout.addWidget(nav_frame)
        main_layout.addWidget(separator)
        main_layout.addWidget(size_frame)
        main_layout.addItem(spacer)
        main_layout.addWidget(self.info_label)
    
    def _apply_styles(self):
        """应用Material Design样式到分页组件"""
        try:
            # 尝试获取样式管理器
            try:
                from src.gui.style_manager import StyleManager
                style_manager = StyleManager.get_instance()
                
                # 为所有按钮应用样式
                buttons = [
                    self.first_page_btn, self.prev_page_btn, self.next_page_btn, 
                    self.last_page_btn, self.jump_btn, self.refresh_btn
                ]
                
                for button in buttons:
                    if button:
                        style_manager.apply_component_style(button, "button_primary")
                        # 添加鼠标悬停样式
                        button.setCursor(Qt.PointingHandCursor)
                
                # 为输入框应用样式
                if self.page_input:
                    style_manager.apply_component_style(self.page_input, "input")
                
                # 为下拉框应用样式
                if self.page_size_combo:
                    style_manager.apply_component_style(self.page_size_combo, "input")
                
                self.logger.info("分页组件Material Design样式应用成功")
                
            except Exception as e:
                self.logger.warning(f"StyleManager不可用，使用备用样式: {e}")
                self._apply_fallback_styles()
                
        except Exception as e:
            self.logger.error(f"分页组件样式应用失败: {e}")
    
    def _apply_fallback_styles(self):
        """应用现代化备用样式 - Material Design 3.0"""
        fallback_button_style = """
            QPushButton {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #4285f4,
                    stop: 1 #1976d2
                );
                color: white;
                border: none;
                border-radius: 12px;
                padding: 10px 18px;
                font-size: 13px;
                font-weight: 600;
                font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
                text-align: center;
                min-width: 40px;
                min-height: 36px;
            }
            QPushButton:hover {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #1976d2,
                    stop: 1 #1565c0
                );
                transform: translateY(-1px);
                box-shadow: 0 4px 12px rgba(66, 133, 244, 0.3);
            }
            QPushButton:pressed {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #1565c0,
                    stop: 1 #0d47a1
                );
                transform: translateY(0px);
            }
            QPushButton:disabled {
                background: qlineargradient(
                    x1: 0, y1: 0, x2: 1, y2: 0,
                    stop: 0 #e0e0e0,
                    stop: 1 #bdbdbd
                );
                color: #9e9e9e;
                transform: none;
                box-shadow: none;
            }
        """

        fallback_input_style = """
            QLineEdit, QComboBox {
                border: 2px solid #e8f0fe;
                border-radius: 8px;
                padding: 8px 12px;
                font-size: 13px;
                font-family: 'Segoe UI', 'Microsoft YaHei', sans-serif;
                background: qlineargradient(x1: 0, y1: 0, x2: 0, y2: 1,
                    stop: 0 #ffffff, stop: 1 #f8f9fa);
                color: #1a1a1a;
                selection-background-color: #4285f4;
            }
            QLineEdit:focus, QComboBox:focus {
                border-color: #4285f4;
                background: white;
                box-shadow: 0 0 0 3px rgba(66, 133, 244, 0.1);
            }
            QLineEdit:hover, QComboBox:hover {
                border-color: #1976d2;
                background: white;
            }
            QComboBox::drop-down {
                border: none;
                background: transparent;
                width: 20px;
            }
            QComboBox::down-arrow {
                image: none;
                border: 2px solid #4285f4;
                border-radius: 3px;
                width: 6px;
                height: 6px;
                border-top: none;
                border-right: none;
                transform: rotate(-45deg);
            }
        """
        
        # 应用备用按钮样式
        buttons = [
            self.first_page_btn, self.prev_page_btn, self.next_page_btn, 
            self.last_page_btn, self.jump_btn, self.refresh_btn
        ]
        
        for button in buttons:
            if button:
                button.setStyleSheet(fallback_button_style)
                button.setCursor(Qt.PointingHandCursor)
        
        # 应用备用输入框样式
        if self.page_input:
            self.page_input.setStyleSheet(fallback_input_style)
        
        if self.page_size_combo:
            self.page_size_combo.setStyleSheet(fallback_input_style)
    
    def _connect_signals(self):
        """连接信号和槽"""
        if self.first_page_btn:
            self.first_page_btn.clicked.connect(self.goto_first_page)
        if self.prev_page_btn:
            self.prev_page_btn.clicked.connect(self.goto_prev_page)
        if self.next_page_btn:
            self.next_page_btn.clicked.connect(self.goto_next_page)
        if self.last_page_btn:
            self.last_page_btn.clicked.connect(self.goto_last_page)
        if self.jump_btn:
            self.jump_btn.clicked.connect(self._on_jump_clicked)
        if self.page_input:
            self.page_input.returnPressed.connect(self._on_page_input_enter)
        if self.page_size_combo:
            self.page_size_combo.currentIndexChanged.connect(self._on_page_size_changed)
        if self.refresh_btn:
            self.refresh_btn.clicked.connect(self.refresh_requested.emit)
    
    def _update_ui_state(self):
        """更新UI状态"""
        # 更新按钮状态
        if self.first_page_btn:
            self.first_page_btn.setEnabled(self.state.current_page > 1)
        if self.prev_page_btn:
            self.prev_page_btn.setEnabled(self.state.current_page > 1)
        if self.next_page_btn:
            self.next_page_btn.setEnabled(self.state.current_page < self.state.total_pages)
        if self.last_page_btn:
            self.last_page_btn.setEnabled(self.state.current_page < self.state.total_pages)
        
        # 更新页码输入框
        if self.page_input:
            self.page_input.setText(str(self.state.current_page))
        
        # 更新总页数标签
        if self.total_pages_label:
            self.total_pages_label.setText(f"/ {self.state.total_pages}页")
        
        # 更新统计信息
        if self.info_label:
            if self.state.total_records > 0:
                info_text = (
                    f"共{self.state.total_records}条记录，"
                    f"显示第{self.state.start_record}-{self.state.end_record}条"
                )
            else:
                info_text = "共0条记录"
            self.info_label.setText(info_text)
    
    def set_total_records(self, total: int):
        """设置总记录数"""
        if total < 0:
            total = 0
        self.state.total_records = total
        self.state.calculate_derived_fields()
        self._update_ui_state()
        self.logger.info(f"总记录数设置为: {total}")
    
    def get_current_page(self) -> int:
        """获取当前页码"""
        return self.state.current_page
    
    def get_page_size(self) -> int:
        """获取页面大小"""
        return self.state.page_size
    
    def set_current_page(self, page: int):
        """设置当前页码"""
        # 🚀 [智能防抖v2.0] 智能防抖检查
        if self.smart_debounce:
            # 使用智能防抖系统
            record_count = getattr(self.state, 'total_records', 0)
            
            # 检查是否应该立即处理
            if self.smart_debounce.should_process_immediately('page_change', record_count):
                self.logger.debug(f"⚡ [智能防抖] 立即处理分页请求: 第{page}页")
            else:
                # 获取智能延迟时间
                optimal_delay = self.smart_debounce.get_optimal_delay('page_change', record_count)
                current_time = time.time()
                
                if hasattr(self, '_last_page_change_time'):
                    time_diff = (current_time - self._last_page_change_time) * 1000  # 转换为毫秒
                    if time_diff < optimal_delay:
                        self.logger.debug(
                            f"⚡ [智能防抖] 延迟{optimal_delay}ms，忽略快速分页: 第{page}页 "
                            f"(已过{time_diff:.0f}ms)"
                        )
                        return
                
                self._last_page_change_time = current_time
        else:
            # 回退到原有防抖机制（优化为20ms）
            current_time = time.time()
            if hasattr(self, '_last_page_change_time') and (current_time - self._last_page_change_time) < self._debounce_delay:
                self.logger.debug(f"🚀 [固定防抖] 忽略快速连续的页面变更请求")
                return
            self._last_page_change_time = current_time
        if page < 1:
            page = 1
        elif page > self.state.total_pages and self.state.total_pages > 0:
            page = self.state.total_pages
        
        if page != self.state.current_page:
            self.state.current_page = page
            self.state.calculate_derived_fields()
            self._update_ui_state()
            
            # 🔧 [静默模式修复] 只在非静默模式下发射信号
            if not self._silent_update_mode:
                self.page_changed.emit(page)
                self.logger.info(f"页码切换到: {page}")
            else:
                self.logger.info(f"页码静默切换到: {page}（不触发信号）")
    
    def set_page_size(self, size: int):
        """设置页面大小"""
        if size > 0 and size != self.state.page_size:
            self.state.page_size = size
            self.state.calculate_derived_fields()
            # 确保当前页不超出范围
            if self.state.current_page > self.state.total_pages:
                self.state.current_page = max(1, self.state.total_pages)
                self.state.calculate_derived_fields()
            self._update_ui_state()
            self.page_size_changed.emit(size)
            self.logger.info(f"页面大小设置为: {size}")
    
    def get_current_state(self):
        """获取当前分页状态"""
        return self.state
    
    def set_silent_update_mode(self, silent: bool):
        """🔧 [静默模式修复] 设置静默更新模式
        
        Args:
            silent: True时不发射信号，False时正常发射信号
        """
        self._silent_update_mode = silent
        if silent:
            self.logger.debug("分页组件进入静默模式，暂停信号发射")
        else:
            self.logger.debug("分页组件退出静默模式，恢复信号发射")
    
    def silent_set_current_page(self, page: int):
        """🔧 [静默模式修复] 静默设置当前页码，不触发信号
        
        专用于避免信号循环的静默更新方法
        """
        old_silent_mode = self._silent_update_mode
        self._silent_update_mode = True
        try:
            self.set_current_page(page)
        finally:
            self._silent_update_mode = old_silent_mode
    
    def reset(self):
        """重置分页状态"""
        self.state = PaginationState()
        self.state.page_size = self.default_page_size
        self._update_ui_state()
        self.logger.info("分页状态已重置")

    def refresh_pagination_state(self):
        """
        🔧 [P2-1] 刷新分页组件状态

        确保分页组件与实际数据量保持同步
        """
        try:
            self.logger.info("🔧 [P2-1] 开始刷新分页组件状态")

            # 🔧 [P2-1] 重新计算派生字段
            self.state.calculate_derived_fields()

            # 🔧 [P2-1] 验证当前页是否有效
            if self.state.current_page > self.state.total_pages and self.state.total_pages > 0:
                self.logger.warning(f"🔧 [P2-1] 当前页{self.state.current_page}超出范围，调整到第{self.state.total_pages}页")
                self.state.current_page = self.state.total_pages
                self.state.calculate_derived_fields()

            # 🔧 [P2-1] 更新UI状态
            self._update_ui_state()

            # 🔧 [P2-1] 验证状态一致性
            self._verify_pagination_consistency()

            self.logger.info(f"🔧 [P2-1] 分页状态刷新完成: 第{self.state.current_page}页/{self.state.total_pages}页, 共{self.state.total_records}条记录")

        except Exception as e:
            self.logger.error(f"🔧 [P2-1] 刷新分页状态失败: {e}")

    def sync_with_data_source(self, total_records: int, current_data_count: int = None):
        """
        🔧 [P2-1] 与数据源同步状态

        Args:
            total_records: 数据源总记录数
            current_data_count: 当前页实际数据条数（可选）
        """
        try:
            self.logger.info(f"🔧 [P2-1] 开始与数据源同步: 总记录数={total_records}, 当前页数据={current_data_count}")

            # 🔧 [P2-1] 更新总记录数
            old_total = self.state.total_records
            self.state.total_records = max(0, total_records)

            # 🔧 [P2-1] 重新计算分页信息
            self.state.calculate_derived_fields()

            # 🔧 [P2-1] 检查当前页是否仍然有效
            if self.state.current_page > self.state.total_pages and self.state.total_pages > 0:
                self.logger.warning(f"🔧 [P2-1] 数据量变化导致当前页无效，从第{self.state.current_page}页调整到第{self.state.total_pages}页")
                self.state.current_page = self.state.total_pages
                self.state.calculate_derived_fields()

            # 🔧 [P2-1] 验证当前页数据量
            if current_data_count is not None:
                expected_count = min(self.state.page_size,
                                   self.state.total_records - (self.state.current_page - 1) * self.state.page_size)
                if current_data_count != expected_count:
                    self.logger.warning(f"🔧 [P2-1] 数据量不匹配: 期望{expected_count}条，实际{current_data_count}条")

            # 🔧 [P2-1] 更新UI
            self._update_ui_state()

            # 🔧 [P2-1] 记录同步结果
            if old_total != self.state.total_records:
                self.logger.info(f"🔧 [P2-1] 数据源同步完成: 总记录数从{old_total}更新为{self.state.total_records}")
            else:
                self.logger.debug("🔧 [P2-1] 数据源同步完成: 无变化")

        except Exception as e:
            self.logger.error(f"🔧 [P2-1] 数据源同步失败: {e}")

    def _verify_pagination_consistency(self):
        """
        🔧 [P2-1] 验证分页状态一致性
        """
        try:
            issues = []

            # 检查基本数值有效性
            if self.state.current_page < 1:
                issues.append(f"当前页码无效: {self.state.current_page}")

            if self.state.page_size <= 0:
                issues.append(f"页面大小无效: {self.state.page_size}")

            if self.state.total_records < 0:
                issues.append(f"总记录数无效: {self.state.total_records}")

            # 检查页码范围
            if self.state.total_pages > 0 and self.state.current_page > self.state.total_pages:
                issues.append(f"当前页码超出范围: {self.state.current_page} > {self.state.total_pages}")

            # 检查记录号范围
            if self.state.total_records > 0:
                if self.state.start_record <= 0:
                    issues.append(f"起始记录号无效: {self.state.start_record}")

                if self.state.end_record > self.state.total_records:
                    issues.append(f"结束记录号超出范围: {self.state.end_record} > {self.state.total_records}")

            # 报告问题
            if issues:
                self.logger.warning(f"🔧 [P2-1] 分页状态一致性问题: {issues}")
                return False
            else:
                self.logger.debug("🔧 [P2-1] 分页状态一致性验证通过")
                return True

        except Exception as e:
            self.logger.error(f"🔧 [P2-1] 分页状态一致性验证失败: {e}")
            return False
    
    def goto_first_page(self):
        """跳转到第一页"""
        self.set_current_page(1)
    
    def goto_prev_page(self):
        """跳转到上一页"""
        self.set_current_page(self.state.current_page - 1)
    
    def goto_next_page(self):
        """跳转到下一页"""
        self.set_current_page(self.state.current_page + 1)
    
    def goto_last_page(self):
        """跳转到最后一页"""
        self.set_current_page(self.state.total_pages)
    
    def _on_page_input_enter(self):
        """页码输入框回车事件"""
        if self.page_input:
            try:
                page = int(self.page_input.text())
                self.set_current_page(page)
            except ValueError:
                self.page_input.setText(str(self.state.current_page))
                self.logger.warning("输入的页码无效")
    
    def _on_jump_clicked(self):
        """跳转按钮点击事件"""
        self._on_page_input_enter()
    
    def _on_page_size_changed(self, index: int):
        """页面大小改变事件"""
        if 0 <= index < len(self.page_size_options):
            size = self.page_size_options[index]
            self.set_page_size(size)
    
    # 🆕 路径状态同步器集成方法
    def apply_inherited_sort_state(self, sort_state: Dict[str, Any]):
        """应用从导航路径继承的排序状态"""
        try:
            if sort_state and isinstance(sort_state, dict):
                sort_columns = sort_state.get('sort_columns', [])
                if isinstance(sort_columns, list):
                    self.state.update_sort_state(sort_columns)
                    self.state.inherited_from_navigation = True
                    
                    # 发出排序状态变化信号
                    self.sort_state_changed.emit(sort_columns)
                    
                    self.logger.info(f"已应用继承的排序状态: {len(sort_columns)} 列")
                else:
                    self.logger.warning("排序状态格式不正确")
            else:
                self.logger.debug("未提供有效的排序状态")
        except Exception as e:
            self.logger.error(f"应用继承排序状态失败: {e}")
    
    def apply_inherited_field_mapping(self, field_mapping: Dict[str, str]):
        """应用从导航路径继承的字段映射"""
        try:
            if field_mapping and isinstance(field_mapping, dict):
                self.state.field_mapping = field_mapping.copy()
                self.logger.info(f"已应用继承的字段映射: {len(field_mapping)} 个字段")
            else:
                self.logger.debug("未提供有效的字段映射")
        except Exception as e:
            self.logger.error(f"应用继承字段映射失败: {e}")
    
    def set_data_source_context(self, context: Dict[str, Any]):
        """设置数据源上下文"""
        try:
            if context and isinstance(context, dict):
                self.state.session_id = context.get('session_id')
                self.state.table_type = context.get('table_type', '')
                
                # 更新数据路径类型
                self.state.data_path_type = "pagination"
                
                self.logger.info(f"已设置数据源上下文: 会话={self.state.session_id}, 表类型={self.state.table_type}")
            else:
                self.logger.debug("未提供有效的数据源上下文")
        except Exception as e:
            self.logger.error(f"设置数据源上下文失败: {e}")
    
    def save_current_sort_state(self) -> Dict[str, Any]:
        """保存当前排序状态"""
        try:
            return {
                'sort_columns': self.state.sort_columns.copy(),
                'table_type': self.state.table_type,
                'session_id': self.state.session_id,
                'current_page': self.state.current_page,
                'page_size': self.state.page_size,
                'field_mapping': self.state.field_mapping.copy(),
                'last_sort_time': self.state.last_sort_time
            }
        except Exception as e:
            self.logger.error(f"保存排序状态失败: {e}")
            return {}
    
    def restore_sort_state(self, sort_context: Dict[str, Any]):
        """恢复排序状态"""
        try:
            if sort_context and isinstance(sort_context, dict):
                sort_columns = sort_context.get('sort_columns', [])
                if sort_columns:
                    self.state.update_sort_state(sort_columns)
                    
                # 恢复其他状态
                if 'table_type' in sort_context:
                    self.state.table_type = sort_context['table_type']
                if 'session_id' in sort_context:
                    self.state.session_id = sort_context['session_id']
                if 'field_mapping' in sort_context:
                    self.state.field_mapping = sort_context['field_mapping'].copy()
                
                # 发出排序状态变化信号
                if sort_columns:
                    self.sort_state_changed.emit(sort_columns)
                
                self.logger.info(f"已恢复排序状态: {len(sort_columns)} 列")
            else:
                self.logger.debug("未提供有效的排序上下文")
        except Exception as e:
            self.logger.error(f"恢复排序状态失败: {e}")
    
    def get_current_sort_info(self) -> Dict[str, Any]:
        """获取当前排序信息"""
        return {
            'has_sort': self.state.has_sort_state(),
            'sort_description': self.state.get_sort_description(),
            'sort_columns': self.state.sort_columns.copy(),
            'table_type': self.state.table_type,
            'sort_dirty': self.state.sort_dirty,
            'inherited_from_navigation': self.state.inherited_from_navigation
        }
    
    def sync_sort_state_with_table(self):
        """🔧 [修复标识] 与表格组件同步排序状态
        
        这个函数用于在分页切换时同步分页组件与表格组件的排序状态，
        确保排序状态的一致性。
        """
        try:
            if hasattr(self, 'table_widget') and self.table_widget:
                # 从表格组件获取当前排序状态
                header = self.table_widget.horizontalHeader()
                current_column = header.sortIndicatorSection()
                current_order = header.sortIndicatorOrder()
                
                if current_column >= 0:
                    # 获取列名
                    column_name = ""
                    if current_column < self.table_widget.columnCount():
                        header_item = self.table_widget.horizontalHeaderItem(current_column)
                        if header_item:
                            column_name = header_item.text()
                    
                    # 转换排序顺序
                    from PyQt5.QtCore import Qt
                    sort_order = "ascending" if current_order == Qt.AscendingOrder else "descending"
                    
                    # 更新分页组件的排序状态
                    self.state.update_sort_state([{
                        'column_name': column_name,
                        'order': sort_order,
                        'priority': 1
                    }])
                    
                    self.logger.debug(f"🔧 [修复标识] 同步排序状态: {column_name} -> {sort_order}")
                else:
                    # 清除排序状态
                    self.state.update_sort_state([])
                    self.logger.debug("🔧 [修复标识] 清除排序状态")
                    
        except Exception as e:
            self.logger.error(f"🔧 [修复标识] 同步排序状态失败: {e}")
    
    def ensure_sort_state_consistency(self):
        """🔧 [修复标识] 确保排序状态一致性
        
        在页面切换或数据更新时调用，确保分页组件和表格组件的排序状态一致。
        """
        try:
            if not hasattr(self, 'table_widget') or not self.table_widget:
                return
            
            # 获取分页组件的排序状态
            pagination_sort_columns = self.state.sort_columns
            
            # 获取表格组件的排序状态
            header = self.table_widget.horizontalHeader()
            table_sort_column = header.sortIndicatorSection()
            table_sort_order = header.sortIndicatorOrder()
            
            # 检查是否一致
            if pagination_sort_columns:
                # 分页组件有排序状态
                first_sort = pagination_sort_columns[0]
                expected_order = Qt.AscendingOrder if first_sort.get('order') == 'ascending' else Qt.DescendingOrder
                
                # 找到对应的列索引
                column_name = first_sort.get('column_name', '')
                expected_column = -1
                
                for i in range(self.table_widget.columnCount()):
                    header_item = self.table_widget.horizontalHeaderItem(i)
                    if header_item and header_item.text() == column_name:
                        expected_column = i
                        break
                
                # 如果不一致，更新表格组件的排序状态
                if table_sort_column != expected_column or table_sort_order != expected_order:
                    if expected_column >= 0:
                        header.setSortIndicator(expected_column, expected_order)
                        self.logger.debug(f"🔧 [修复标识] 更新表格排序状态: 列{expected_column}, 顺序{expected_order}")
                    else:
                        header.setSortIndicator(-1, Qt.AscendingOrder)
                        self.logger.debug("🔧 [修复标识] 清除表格排序状态: 列名无效")
            else:
                # 分页组件无排序状态，清除表格组件的排序状态
                if table_sort_column >= 0:
                    header.setSortIndicator(-1, Qt.AscendingOrder)
                    self.logger.debug("🔧 [修复标识] 清除表格排序状态: 分页组件无排序")
                    
        except Exception as e:
            self.logger.error(f"🔧 [修复标识] 确保排序状态一致性失败: {e}") 