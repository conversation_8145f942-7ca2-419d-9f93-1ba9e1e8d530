#!/usr/bin/env python3
"""
测试ArchitectureFactory的get_unified_format_manager方法修复
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_architecture_factory_format_manager():
    """测试ArchitectureFactory能否正确返回格式化管理器"""
    print("🔧 [测试] 开始测试ArchitectureFactory格式化管理器修复...")
    
    try:
        # 1. 导入必要的模块
        from src.core.architecture_factory import ArchitectureFactory
        from src.modules.data_storage.dynamic_table_manager import DynamicTableManager
        from src.modules.system_config import ConfigManager
        
        print("✅ [测试] 模块导入成功")
        
        # 2. 创建依赖组件（模拟）
        config_manager = ConfigManager()
        db_manager = DynamicTableManager(config_manager)
        
        print("✅ [测试] 依赖组件创建成功")
        
        # 3. 创建ArchitectureFactory实例
        factory = ArchitectureFactory(db_manager, config_manager)
        
        print("✅ [测试] ArchitectureFactory创建成功")
        
        # 4. 测试get_unified_format_manager方法是否存在
        if hasattr(factory, 'get_unified_format_manager'):
            print("✅ [测试] get_unified_format_manager方法存在")
        else:
            print("❌ [测试] get_unified_format_manager方法不存在")
            return False
        
        # 5. 测试调用get_unified_format_manager方法
        try:
            format_manager = factory.get_unified_format_manager()
            if format_manager is not None:
                print("✅ [测试] get_unified_format_manager方法调用成功")
                print(f"✅ [测试] 返回的格式化管理器类型: {type(format_manager).__name__}")
                
                # 6. 测试格式化管理器是否有format_data方法
                if hasattr(format_manager, 'format_data'):
                    print("✅ [测试] 格式化管理器具有format_data方法")
                else:
                    print("❌ [测试] 格式化管理器缺少format_data方法")
                    return False
                
                return True
            else:
                print("❌ [测试] get_unified_format_manager返回None")
                return False
                
        except Exception as e:
            print(f"❌ [测试] 调用get_unified_format_manager失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ [测试] 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_format_manager_interface():
    """测试格式化管理器接口兼容性"""
    print("\n🔧 [测试] 开始测试格式化管理器接口兼容性...")
    
    try:
        from src.modules.format_management.unified_format_manager import UnifiedFormatManager
        
        # 创建UnifiedFormatManager实例
        format_manager = UnifiedFormatManager()
        
        print("✅ [测试] UnifiedFormatManager创建成功")
        
        # 测试format_data方法签名
        import inspect
        format_data_signature = inspect.signature(format_manager.format_data)
        print(f"✅ [测试] format_data方法签名: {format_data_signature}")
        
        # 测试基本调用（使用空DataFrame）
        import pandas as pd
        empty_df = pd.DataFrame()
        
        try:
            result = format_manager.format_data(empty_df, "retired_employees", data_source="test")
            print("✅ [测试] format_data方法调用成功")
            print(f"✅ [测试] 返回结果类型: {type(result).__name__}")
            return True
        except Exception as e:
            print(f"❌ [测试] format_data方法调用失败: {e}")
            return False
            
    except Exception as e:
        print(f"❌ [测试] 接口测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🚀 [测试] 开始ArchitectureFactory修复验证测试")
    print("=" * 60)
    
    # 测试1: ArchitectureFactory修复
    test1_result = test_architecture_factory_format_manager()
    
    # 测试2: 格式化管理器接口
    test2_result = test_format_manager_interface()
    
    print("\n" + "=" * 60)
    print("📊 [测试结果]")
    print(f"  ArchitectureFactory修复: {'✅ 通过' if test1_result else '❌ 失败'}")
    print(f"  格式化管理器接口: {'✅ 通过' if test2_result else '❌ 失败'}")
    
    if test1_result and test2_result:
        print("\n🎉 [测试] 所有测试通过！ArchitectureFactory修复成功")
        sys.exit(0)
    else:
        print("\n💥 [测试] 部分测试失败，需要进一步修复")
        sys.exit(1)
