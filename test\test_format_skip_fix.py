#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import sqlite3
import pandas as pd
import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_format_skip_fix():
    """测试格式化跳过修复"""
    try:
        print("=== 测试格式化跳过修复 ===")
        
        # 连接数据库
        conn = sqlite3.connect('data/db/salary_system.db')
        
        # 查询离休人员表的原始数据
        query = 'SELECT * FROM salary_data_2025_07_retired_employees LIMIT 2'
        df = pd.read_sql_query(query, conn)
        
        # 转换为字典格式（模拟表格数据格式）
        data = df.to_dict('records')
        
        print('原始数据:')
        for i, row in enumerate(data):
            print(f'  第{i}行: employee_id={repr(row.get("employee_id"))}, employee_name={repr(row.get("employee_name"))}')
        print()
        
        # 导入格式化管理器
        from src.core.architecture_factory import ArchitectureFactory
        from src.modules.system_config.config_manager import ConfigManager
        from src.modules.data_storage.database_manager import DatabaseManager

        # 初始化必要的组件
        config_manager = ConfigManager()
        db_manager = DatabaseManager()

        # 初始化架构工厂
        factory = ArchitectureFactory(db_manager, config_manager)
        factory.initialize_architecture()
        
        # 获取格式化管理器
        format_manager = factory.get_unified_format_manager()
        
        if format_manager:
            print('✅ 格式化管理器获取成功')
            
            # 尝试格式化数据
            table_type = "retired_employees"
            formatted_df = format_manager.format_data(
                data, table_type, headers=list(df.columns)
            )

            # 转换为字典格式
            formatted_data = formatted_df.to_dict('records')
            formatted_headers = list(formatted_df.columns)

            print('格式化后DataFrame信息:')
            print(f'  形状: {formatted_df.shape}')
            print(f'  列名: {list(formatted_df.columns)}')
            print(f'  前2行数据:')
            for i, (idx, row) in enumerate(formatted_df.iterrows()):
                if i < 2:
                    print(f'    第{i}行: employee_id={repr(row.get("employee_id"))}, employee_name={repr(row.get("employee_name"))}, one_time_living_allowance={repr(row.get("one_time_living_allowance"))}, month={repr(row.get("month"))}, remarks={repr(row.get("remarks"))}')
            
            print('格式化后数据:')
            for i, row in enumerate(formatted_data):
                print(f'  第{i}行: employee_id={repr(row.get("employee_id"))}, employee_name={repr(row.get("employee_name"))}, one_time_living_allowance={repr(row.get("one_time_living_allowance"))}, month={repr(row.get("month"))}, remarks={repr(row.get("remarks"))}')
            print()
            
            # 测试顺序检查逻辑（模拟virtualized_expandable_table.py中的逻辑）
            print('=== 测试顺序检查逻辑 ===')
            if len(formatted_data) > 0 and len(data) > 0:
                # 🔧 [格式化修复] 支持多种ID字段名，包括格式化后的中文字段名
                original_first_id = (data[0].get('工号') or 
                                   data[0].get('employee_id') or 
                                   data[0].get('人员代码') or 
                                   str(data[0])[:20])
                formatted_first_id = (formatted_data[0].get('工号') or
                                    formatted_data[0].get('employee_id') or
                                    formatted_data[0].get('人员代码') or
                                    str(formatted_data[0])[:20])
                
                print(f'原始第一行ID: {repr(original_first_id)}')
                print(f'格式化第一行ID: {repr(formatted_first_id)}')
                
                # 标准化ID值用于比较
                def normalize_id(id_value):
                    if id_value is None:
                        return None
                    id_str = str(id_value)
                    if '.' in id_str and id_str.replace('.', '').replace('-', '').isdigit():
                        try:
                            return str(int(float(id_str)))
                        except (ValueError, TypeError):
                            return id_str
                    return id_str
                
                normalized_original = normalize_id(original_first_id)
                normalized_formatted = normalize_id(formatted_first_id)
                
                print(f'标准化原始ID: {repr(normalized_original)}')
                print(f'标准化格式化ID: {repr(normalized_formatted)}')
                
                # 检查是否匹配
                id_match = normalized_original == normalized_formatted
                cross_match = (normalized_original and normalized_formatted and 
                             normalized_original in str(formatted_first_id) and 
                             normalized_formatted in str(original_first_id))
                
                print(f'ID直接匹配: {id_match}')
                print(f'交叉匹配: {cross_match}')
                print(f'最终结果: {"✅ 应用格式化" if (id_match or cross_match) else "❌ 跳过格式化"}')
                
                if id_match or cross_match:
                    print('\n=== 格式化效果验证 ===')
                    for i, (orig, fmt) in enumerate(zip(data, formatted_data)):
                        print(f'第{i}行:')
                        print(f'  人员代码: {repr(orig.get("employee_id"))} → {repr(fmt.get("employee_id"))}')
                        print(f'  增发一次性生活补贴: {repr(orig.get("one_time_living_allowance"))} → {repr(fmt.get("one_time_living_allowance"))}')
                        print(f'  补发: {repr(orig.get("supplement"))} → {repr(fmt.get("supplement"))}')
                        print(f'  借支: {repr(orig.get("advance"))} → {repr(fmt.get("advance"))}')
                        print(f'  月份: {repr(orig.get("month"))} → {repr(fmt.get("month"))}')
                        print(f'  备注: {repr(orig.get("remarks"))} → {repr(fmt.get("remarks"))}')
                        print()
        else:
            print('❌ 格式化管理器获取失败')
        
        conn.close()
        
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    test_format_skip_fix()
