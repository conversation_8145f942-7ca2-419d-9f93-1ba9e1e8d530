#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试备注字段格式化问题
"""

import pandas as pd
import sys
import os

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.abspath('.'))

def test_debug_remarks_field():
    """调试备注字段格式化"""
    print("🔍 [调试] 开始调试备注字段格式化...")
    print("=" * 60)
    
    try:
        # 1. 创建测试数据
        test_data = pd.DataFrame({
            '备注': [None, '', '测试备注']
        })
        
        print("📊 [测试数据] 原始备注字段:")
        print(test_data)
        print()
        
        # 2. 检查字段类型映射
        from src.modules.format_management.field_registry import FieldRegistry
        field_registry = FieldRegistry("state/data/field_mappings.json")
        
        field_types = field_registry.get_table_field_types("retired_employees")
        print(f"🏷️ [字段类型] retired_employees字段类型映射: {field_types}")
        
        remarks_type = field_types.get('备注', 'NOT_FOUND')
        print(f"🏷️ [备注类型] 备注字段类型: {remarks_type}")
        print()
        
        # 3. 检查格式配置
        from src.modules.format_management.format_config import FormatConfig
        format_config = FormatConfig("state/format_config.json")
        
        string_config = format_config.get_format_rules('string', 'retired_employees')
        print(f"📋 [格式配置] string格式配置: {string_config}")
        print()
        
        # 4. 直接测试格式渲染器
        from src.modules.format_management.format_renderer import FormatRenderer
        renderer = FormatRenderer(format_config, field_registry)
        
        # 测试单个值渲染
        test_values = [None, '', '测试备注']
        print("🎨 [单值渲染] 测试备注字段单值渲染:")
        for value in test_values:
            rendered = renderer._render_string_value(value, string_config)
            print(f"  {repr(value)} -> '{rendered}'")
        print()
        
        # 5. 测试列渲染
        print("🎨 [列渲染] 测试备注字段列渲染:")
        rendered_column = renderer._render_string_column(test_data['备注'], string_config, '备注')
        print(f"  渲染结果: {rendered_column.tolist()}")
        print()
        
        # 6. 测试完整格式化流程
        print("🎨 [完整流程] 测试完整格式化流程:")
        formatted_column = renderer.render_column(test_data['备注'], 'string', '备注', 'retired_employees')
        print(f"  完整流程结果: {formatted_column.tolist()}")
        print()
        
        # 7. 使用UnifiedFormatManager测试
        from src.modules.format_management.unified_format_manager import UnifiedFormatManager
        format_manager = UnifiedFormatManager()
        
        formatted_data = format_manager.format_data(test_data, "retired_employees", data_source="test")
        print("✨ [统一格式管理] UnifiedFormatManager格式化结果:")
        print(formatted_data)
        
        return True
        
    except Exception as e:
        print(f"❌ [调试失败] 调试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_debug_remarks_field()
