#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主格式化管理器 - 统一所有格式化操作
🎯 [统一格式化] 解决多层格式化冲突，建立单一格式化流程

本模块实现了统一的格式化管理，解决数据被多次格式化导致的问题。
主要功能：
1. 格式化状态跟踪
2. 单一格式化入口
3. 格式化缓存管理
4. 配置化格式化规则

作者: 薪资系统重构团队
创建时间: 2025-07-19
版本: v1.0.0
"""

import hashlib
import time
import json
from typing import Any, Dict, List, Optional, Union
import pandas as pd
from pathlib import Path

from src.utils.log_config import setup_logger


class FormatStateTracker:
    """
    格式化状态跟踪器 - 跟踪数据格式化状态
    🚨 [关键组件] 防止重复格式化
    """
    
    def __init__(self):
        self.logger = setup_logger(self.__class__.__name__)
        self._formatted_data_ids = set()
    
    def is_formatted(self, data: Union[pd.DataFrame, List[Dict]]) -> bool:
        """检查数据是否已格式化"""
        try:
            # 检查DataFrame属性标记
            if isinstance(data, pd.DataFrame):
                if hasattr(data, 'attrs') and data.attrs.get('formatted'):
                    return True
                
                # 检查数据特征
                if self._check_dataframe_characteristics(data):
                    return True
            
            # 检查列表数据特征
            elif isinstance(data, list) and data:
                if self._check_list_characteristics(data):
                    return True
            
            # 检查ID缓存
            data_id = self._generate_data_id(data)
            return data_id in self._formatted_data_ids
            
        except Exception as e:
            self.logger.warning(f"格式化状态检查失败: {e}")
            return False
    
    def mark_formatted(self, data: Union[pd.DataFrame, List[Dict]]):
        """标记数据已格式化"""
        try:
            # 设置DataFrame属性标记
            if isinstance(data, pd.DataFrame) and hasattr(data, 'attrs'):
                data.attrs['formatted'] = True
            
            # 添加到ID缓存
            data_id = self._generate_data_id(data)
            self._formatted_data_ids.add(data_id)
            
            self.logger.debug(f"数据已标记为格式化: {data_id[:8]}...")
            
        except Exception as e:
            self.logger.warning(f"标记格式化状态失败: {e}")
    
    def _check_dataframe_characteristics(self, data: pd.DataFrame) -> bool:
        """检查DataFrame特征判断是否已格式化"""
        # 🔧 [P0-CRITICAL] 修复DataFrame条件判断错误
        if data.empty:  # 使用.empty而不是len()判断
            return False
        
        try:
            # 检查货币字段特征
            for column in data.columns:
                if '工资' in column or '津贴' in column or '补贴' in column:
                    # 🔧 [P0-CRITICAL] 安全检查列数据
                    if not data[column].empty:
                        sample_value = data.iloc[0][column]
                        if isinstance(sample_value, str) and '¥' in sample_value:
                            return True
            return False
            
        except Exception:
            return False
    
    def _check_list_characteristics(self, data: List[Dict]) -> bool:
        """检查列表数据特征判断是否已格式化"""
        if not data:
            return False
        
        try:
            first_row = data[0]
            for key, value in first_row.items():
                if '工资' in key or '津贴' in key or '补贴' in key:
                    if isinstance(value, str) and '¥' in value:
                        return True
            return False
            
        except Exception:
            return False
    
    def _generate_data_id(self, data: Union[pd.DataFrame, List[Dict]]) -> str:
        """生成数据唯一标识"""
        try:
            if isinstance(data, pd.DataFrame):
                data_str = f"{data.shape}_{list(data.columns)}_{str(data.iloc[0].values if len(data) > 0 else [])}"
            else:
                data_str = f"{len(data)}_{list(data[0].keys() if data else [])}_{str(data[0] if data else {})}"
            
            return hashlib.md5(data_str.encode()).hexdigest()
            
        except Exception:
            return hashlib.md5(str(time.time()).encode()).hexdigest()


class MasterFormatManager:
    """
    主格式化管理器 - 协调所有格式化操作
    🎯 [统一格式化] 建立单一、清晰的格式化流程
    """
    
    def __init__(self):
        self.logger = setup_logger(self.__class__.__name__)
        self._format_state_tracker = FormatStateTracker()
        self._format_cache = {}
        
        # 🔧 [新系统迁移] 启用新的统一格式管理系统
        self._enabled_formatters = ['unified_format']  # 使用新的统一格式管理
        
        # 延迟加载格式化器实例
        self._formatter_instances = {}
        
        # 🧹 [配置化管理] 加载格式化配置
        self._format_config = self._load_format_config()
        
        # 🔧 [新系统] 初始化统一格式管理组件
        self._initialize_unified_format_system()
        
        # 🚨 [排序修复] 清除可能存在的旧缓存，防止缓存污染
        self.clear_cache()
        
        self.logger.info("🎯 [统一格式化] 主格式化管理器已初始化（新系统）")
    
    def _initialize_unified_format_system(self):
        """
        🔧 [新系统] 初始化统一格式管理系统组件
        """
        try:
            # 初始化字段映射路径
            from pathlib import Path
            field_mappings_path = str(Path(__file__).parent.parent.parent.parent / "state" / "data" / "field_mappings.json")
            format_config_path = str(Path(__file__).parent.parent.parent.parent / "state" / "format_config.json")
            
            # 初始化FieldRegistry
            from .field_registry import FieldRegistry
            self.field_registry = FieldRegistry(field_mappings_path)
            self.field_registry.load_mappings()
            
            # 初始化FormatConfig  
            from .format_config import FormatConfig
            self.format_config = FormatConfig(format_config_path)
            self.format_config.load_config()
            
            # 初始化FormatRenderer
            from .format_renderer import FormatRenderer
            self.format_renderer = FormatRenderer(self.format_config, self.field_registry)
            
            self.logger.info("🔧 [新系统] 统一格式管理系统组件初始化完成")
            
        except Exception as e:
            self.logger.error(f"🔧 [新系统] 统一格式管理系统初始化失败: {e}")
            # 设置为None，将使用回退逻辑
            self.field_registry = None
            self.format_config = None 
            self.format_renderer = None
    
    def format_table_data(self, data: Union[pd.DataFrame, List[Dict]], table_type: str, 
                         force_reformat: bool = False) -> Union[pd.DataFrame, List[Dict]]:
        """
        统一的表格数据格式化入口
        🎯 [统一格式化] 防止重复格式化，提升性能
        
        Args:
            data: 原始数据
            table_type: 表格类型
            force_reformat: 是否强制重新格式化
            
        Returns:
            格式化后的数据
        """
        try:
            # 1. 检查数据是否已格式化
            if not force_reformat and self._format_state_tracker.is_formatted(data):
                self.logger.info("🎯 [统一格式化] 数据已格式化，跳过重复处理")
                return data
            
            # 2. 检查缓存
            cache_key = self._generate_cache_key(data, table_type)
            cached_result = self._format_cache.get(cache_key)
            if cached_result is not None and not force_reformat:
                self.logger.debug("🎯 [统一格式化] 使用缓存结果")
                return cached_result
            
            # 3. 执行格式化
            formatted_data = self._execute_formatting(data, table_type)
            
            # 4. 🔧 [P2-优化] 验证格式化完整性后再标记
            if self._verify_formatting_completeness(formatted_data, table_type):
                self._format_state_tracker.mark_formatted(formatted_data)
                self.logger.debug(f"🎯 [统一格式化] 格式化验证通过，已标记: {table_type}")
            else:
                self.logger.warning(f"🎯 [统一格式化] 格式化验证未通过，不标记: {table_type}")
            
            # 5. 缓存结果（限制缓存大小）
            if len(self._format_cache) < 100:  # 防止内存泄漏
                self._format_cache[cache_key] = formatted_data
            
            self.logger.info(f"🎯 [统一格式化] 数据格式化完成: {table_type}")
            return formatted_data
            
        except Exception as e:
            # 🔧 [P0-修复] 移除防护性掩盖代码，直接处理错误
            self.logger.error(f"🎯 [统一格式化] 格式化失败: {e}")
            # 清理缓存确保数据一致性
            self.clear_cache()
            return data  # 返回原始数据，不中断流程
    
    def format_display_value(self, value: Any, column_name: str) -> str:
        """
        🔧 [新系统] 格式化显示值 - 使用统一格式管理系统
        
        Args:
            value: 原始值
            column_name: 列名
            
        Returns:
            格式化后的字符串
        """
        try:
            # 使用新系统的字段类型判断
            field_type = self.get_field_type(column_name)
            
            if field_type == 'float':
                return self._format_float_value(value)
            elif field_type == 'month_string':
                return self._format_month_string_value(value)
            elif field_type == 'year_string':
                return self._format_year_string_value(value)
            elif field_type == 'employee_code':
                return self._format_employee_code_value(value)
            elif field_type == 'department_code':
                return self._format_department_code_value(value)
            elif field_type == 'string':
                return str(value) if value is not None else ""
            else:
                return str(value) if value is not None else ""
            
        except Exception as e:
            self.logger.warning(f"🔧 [新系统] 显示值格式化失败: {e}")
            return str(value) if value is not None else ""
    
    def _format_float_value(self, value: Any) -> str:
        """格式化浮点数值（两位小数，无符号）"""
        try:
            if value is None or value == '' or str(value).lower() in ['nan', 'none']:
                return "0.00"
            
            numeric_value = float(value)
            
            # 格式化为两位小数，不使用千分位分隔符，不加任何符号
            formatted_value = f"{numeric_value:.2f}"
            
            return formatted_value
            
        except (ValueError, TypeError):
            return "0.00"
    
    def _format_month_string_value(self, value: Any) -> str:
        """格式化月份字符串值（提取后两位）"""
        try:
            if value is None or value == '' or str(value).lower() in ['nan', 'none']:
                return ""
            
            # 转换为字符串并提取后两位月份
            month_str = str(value).strip()
            
            if month_str.isdigit():
                if len(month_str) >= 2:
                    return month_str[-2:]
                else:
                    return month_str.zfill(2)  # 不足两位补零
            else:
                # 尝试提取数字部分
                import re
                digits = re.findall(r'\d+', month_str)
                if digits:
                    month_num = digits[-1]  # 取最后一个数字序列
                    if len(month_num) >= 2:
                        return month_num[-2:]
                    else:
                        return month_num.zfill(2)
                else:
                    return ""
                    
        except Exception:
            return ""
    
    def _format_year_string_value(self, value: Any) -> str:
        """格式化年份字符串值"""
        try:
            if value is None or value == '' or str(value).lower() in ['nan', 'none']:
                return ""
            
            # 转换为字符串
            year_str = str(value).strip()
            
            if year_str.replace('.', '').isdigit():
                # 数字格式（可能包含小数点）
                try:
                    year_int = int(float(year_str))
                    return str(year_int)
                except (ValueError, OverflowError):
                    return ""
            else:
                # 尝试提取年份
                import re
                year_match = re.search(r'(20\d{2}|19\d{2})', year_str)
                if year_match:
                    return year_match.group(1)
                else:
                    return ""
                        
        except Exception:
            return ""
    
    def _format_employee_code_value(self, value: Any) -> str:
        """格式化工号（去除小数点，保持整数）"""
        try:
            if value is None or value == '' or str(value).lower() in ['nan', 'none']:
                return ""
            
            # 🔧 [用户需求修复] 工号从 19990089.0 格式化为 19990089
            # 转为浮点数再转为整数，去除小数点
            numeric_value = float(value)
            integer_value = int(numeric_value)
            
            self.logger.debug(f"🔧 [工号格式化] {value} -> {integer_value}")
            return str(integer_value)
            
        except (ValueError, TypeError, OverflowError) as e:
            self.logger.warning(f"🔧 [工号格式化] 格式化失败 {value}: {e}")
            return str(value) if value is not None else ""
    
    def _format_department_code_value(self, value: Any) -> str:
        """格式化部门代码（去小数点，补零到两位）"""
        try:
            if value is None or value == '' or str(value).lower() in ['nan', 'none']:
                return ""
            
            # 🔧 [用户需求修复] 人员类别代码从 1.0 格式化为 01
            # 转为浮点数再转为整数，然后补零到两位
            numeric_value = float(value)
            integer_value = int(numeric_value)
            
            # 补零到两位数字
            formatted_code = f"{integer_value:02d}"
            
            self.logger.debug(f"🔧 [部门代码格式化] {value} -> {formatted_code}")
            return formatted_code
            
        except (ValueError, TypeError, OverflowError) as e:
            self.logger.warning(f"🔧 [部门代码格式化] 格式化失败 {value}: {e}")
            return str(value) if value is not None else ""
    
    def _execute_formatting(self, data: Union[pd.DataFrame, List[Dict]], table_type: str) -> Union[pd.DataFrame, List[Dict]]:
        """
        🔧 [新系统] 执行具体的格式化逻辑 - 使用统一格式管理系统
        """
        try:
            # 优先使用新的统一格式管理系统
            if 'unified_format' in self._enabled_formatters and self.format_renderer:
                if isinstance(data, pd.DataFrame):
                    return self.format_renderer.render_dataframe(data, table_type)
                else:
                    # 对于列表数据，先转为DataFrame处理
                    if isinstance(data, list) and len(data) > 0:
                        df = pd.DataFrame(data)
                        formatted_df = self.format_renderer.render_dataframe(df, table_type)
                        return formatted_df.to_dict('records')
            
            # 🔧 [回退机制] 如果新系统不可用，记录日志但不使用旧系统
            else:
                self.logger.warning("🔧 [新系统] 统一格式管理系统不可用，跳过格式化")
            
            return data
            
        except Exception as e:
            self.logger.error(f"🔧 [新系统] 格式化执行失败: {e}")
            return data
    
    # 🔧 [旧系统删除] 不再需要SalaryDataFormatter
    
    def _generate_cache_key(self, data: Union[pd.DataFrame, List[Dict]], table_type: str) -> str:
        """
        生成缓存键 - 🚨 [排序修复] 基于数据内容，不只是结构
        """
        try:
            if isinstance(data, pd.DataFrame):
                # 🚨 [排序修复] 包含数据内容的缓存键，防止排序数据被缓存污染
                if not data.empty:
                    # 使用前几行关键数据生成内容哈希
                    first_row_data = str(data.iloc[0].to_dict()) if len(data) > 0 else ""
                    last_row_data = str(data.iloc[-1].to_dict()) if len(data) > 0 else ""
                    content_signature = f"{first_row_data}_{last_row_data}"
                else:
                    content_signature = "empty"
                
                data_hash = f"{data.shape}_{len(data.columns)}_{table_type}_{content_signature}"
            else:
                # 🚨 [排序修复] 列表数据也包含内容哈希
                if data and len(data) > 0:
                    first_row = str(data[0]) if len(data) > 0 else ""
                    last_row = str(data[-1]) if len(data) > 0 else ""
                    content_signature = f"{first_row}_{last_row}"
                else:
                    content_signature = "empty"
                
                data_hash = f"{len(data)}_{len(data[0]) if data else 0}_{table_type}_{content_signature}"
            
            return hashlib.md5(data_hash.encode()).hexdigest()[:16]
            
        except Exception:
            # 🚨 [排序修复] 缓存键生成失败时，返回基于时间的唯一键，确保不使用错误缓存
            return f"cache_{int(time.time() * 1000)}"
    
    def clear_cache(self):
        """清理格式化缓存"""
        self._format_cache.clear()
        self.logger.info("🎯 [统一格式化] 格式化缓存已清理")
    
    def get_format_stats(self) -> Dict[str, Any]:
        """获取格式化统计信息"""
        return {
            'cache_size': len(self._format_cache),
            'formatted_data_count': len(self._format_state_tracker._formatted_data_ids),
            'enabled_formatters': self._enabled_formatters.copy(),
            'config_loaded': self._format_config is not None
        }
    
    def _load_format_config(self) -> Dict[str, Any]:
        """
        🧹 [配置化管理] 加载格式化配置文件
        """
        try:
            # 🔧 [修复] 使用正确的配置文件路径
            config_path = Path(__file__).parent.parent.parent.parent / "state" / "format_config.json"
            
            if config_path.exists():
                with open(config_path, 'r', encoding='utf-8') as f:
                    config = json.load(f)
                self.logger.info(f"格式化配置加载成功: {config_path}")
                return config
            else:
                self.logger.warning(f"配置文件不存在: state/data/format_config.json")
                return self._create_default_config()
                
        except Exception as e:
            self.logger.error(f"配置加载失败: {e}")
            return self._create_default_config()
    
    def _create_default_config(self) -> Dict[str, Any]:
        """创建默认配置"""
        return {
            "defaults": {
                "float": {
                    "decimal_places": 2,
                    "zero_display": "0.00",
                    "error_display": "0.00"
                }
            },
            "tables": {
                "active_employees": {
                    "fields": {
                        "2025年岗位工资": {"type": "float"},
                        "2025年薪级工资": {"type": "float"},
                        "津贴": {"type": "float"}
                    }
                }
            }
        }
    
    def get_field_type(self, field_name: str, table_type: str = 'active_employees') -> str:
        """
        🧹 [新系统] 通过FieldRegistry获取字段类型
        """
        try:
            # 使用统一的FieldRegistry获取字段类型
            if hasattr(self, 'field_registry') and self.field_registry:
                # 🔧 [修复] 映射简化表名到完整表名
                table_name_mapping = {
                    'active_employees': 'salary_data_2025_07_active_employees',
                    'pension_employees': 'salary_data_2025_07_pension_employees',
                    'retired_employees': 'salary_data_2025_07_retired_employees',
                    'a_grade_employees': 'salary_data_2025_07_a_grade_employees'
                }
                
                # 尝试映射到完整表名
                full_table_type = table_name_mapping.get(table_type, table_type)
                
                field_type = self.field_registry.get_field_type(field_name, full_table_type)
                if field_type:
                    self.logger.debug(f"🔧 [字段类型] FieldRegistry找到类型: {field_name} -> {field_type} (表: {full_table_type})")
                    return field_type
                else:
                    self.logger.debug(f"🔧 [字段类型] FieldRegistry未找到类型: {field_name}, table_type: {full_table_type}")
            
            # 🔧 [用户需求修复] 问题字段的明确类型映射
            problem_fields_mapping = {
                # 🆕 [代码类字段] - 新增用户需求的关键字段类型
                '工号': 'employee_code',
                'employee_id': 'employee_code',
                '人员代码': 'employee_code',
                '人员类别代码': 'department_code',
                'employee_type_code': 'department_code',
                
                # 浮点型字段（两位小数）
                '2025年基础性绩效': 'float',
                'basic_performance_2025': 'float',
                '卫生费': 'float',
                'health_fee': 'float',
                '车补': 'float',
                'car_allowance': 'float',
                '2025年奖励性绩效预发': 'float',
                'performance_bonus_2025': 'float',
                '补发': 'float',
                'supplement': 'float',
                '借支': 'float',
                'advance': 'float',
                # 🔧 [离休人员表修复] 增加缺失的浮点数字段
                '增发一次性生活补贴': 'float',
                'one_time_living_allowance': 'float',
                '基本离休费': 'float',
                'basic_retirement_salary': 'float',
                '结余津贴': 'float',
                'balance_allowance': 'float',
                '生活补贴': 'float',
                'living_allowance': 'float',
                '护理费': 'float',
                'nursing_fee': 'float',
                '离休补贴': 'float',
                'retirement_allowance': 'float',
                '合计': 'float',
                'total': 'float',
                '2025公积金': 'float',
                'provident_fund_2025': 'float',
                '代扣代存养老保险': 'float',
                'pension_insurance': 'float',
                # 其他浮点型工资字段
                '2025年岗位工资': 'float',
                'position_salary_2025': 'float',
                '2025年薪级工资': 'float',
                'grade_salary_2025': 'float',
                '津贴': 'float',
                'allowance': 'float',
                '结余津贴': 'float',
                'balance_allowance': 'float',
                '交通补贴': 'float',
                'transport_allowance': 'float',
                '物业补贴': 'float',
                'property_allowance': 'float',
                '住房补贴': 'float',
                'housing_allowance': 'float',
                '通讯补贴': 'float',
                'communication_allowance': 'float',
                '应发工资': 'float',
                'total_salary': 'float',
                # 特殊字符串类型
                '月份': 'month_string',
                'month': 'month_string',
                '年份': 'year_string',
                'year': 'year_string'
            }
            
            # 直接匹配问题字段
            if field_name in problem_fields_mapping:
                field_type = problem_fields_mapping[field_name]
                self.logger.debug(f"🔧 [字段类型] 问题字段映射找到: {field_name} -> {field_type}")
                return field_type
            
            # 回退到关键词匹配
            if any(keyword in field_name for keyword in ['工资', '津贴', '补贴', '绩效', '奖金', '公积金', '保险']):
                self.logger.debug(f"🔧 [字段类型] 关键词匹配(工资类): {field_name} -> float")
                return 'float'
            elif field_name in ['月份', 'month']:
                self.logger.debug(f"🔧 [字段类型] 月份字段匹配: {field_name} -> month_string")
                return 'month_string'
            elif field_name in ['年份', 'year']:
                self.logger.debug(f"🔧 [字段类型] 年份字段匹配: {field_name} -> year_string")
                return 'year_string'
            elif any(keyword in field_name for keyword in ['工号', '代码']):
                self.logger.debug(f"🔧 [字段类型] 关键词匹配(代码类): {field_name} -> string")
                return 'string'
            
            # 默认为字符串
            self.logger.debug(f"🔧 [字段类型] 默认字符串类型: {field_name} -> string")
            return 'string'
            
        except Exception as e:
            self.logger.warning(f"字段类型检测失败: {e}")
            return 'string'
    
    def _verify_formatting_completeness(self, data: Union[pd.DataFrame, List[Dict]], table_type: str) -> bool:
        """
        🔧 [P2-优化] 验证格式化完整性
        
        Args:
            data: 格式化后的数据
            table_type: 表格类型
            
        Returns:
            bool: True表示格式化完整，False表示格式化不完整
        """
        try:
            if data is None or (isinstance(data, pd.DataFrame) and data.empty) or (isinstance(data, list) and not data):
                return True  # 空数据默认通过
            
            # 🔧 [用户需求验证] 检查关键问题字段的格式化
            problem_fields = {
                # 🆕 [代码类字段] - 用户报告的核心问题字段
                '工号': 'employee_code',
                'employee_id': 'employee_code',
                '人员代码': 'employee_code',
                '人员类别代码': 'department_code',
                'employee_type_code': 'department_code',
                
                # 现有的问题字段
                '2025年基础性绩效': 'float',
                '卫生费': 'float', 
                '车补': 'float',
                '2025年奖励性绩效预发': 'float',
                '补发': 'float',
                '借支': 'float',
                '2025公积金': 'float',
                '代扣代存养老保险': 'float',
                '月份': 'month_string'
            }
            
            # 获取第一行数据进行验证
            if isinstance(data, pd.DataFrame):
                if len(data) == 0:
                    return True
                first_row = data.iloc[0]
                columns = data.columns.tolist()
            else:
                if len(data) == 0:
                    return True
                first_row = data[0]
                columns = list(first_row.keys()) if first_row else []
            
            # 检查问题字段是否正确格式化
            failed_fields = []
            for field_name, expected_type in problem_fields.items():
                if field_name in columns:
                    value = first_row.get(field_name) if hasattr(first_row, 'get') else first_row[field_name]
                    
                    if expected_type == 'float':
                        if not self._is_float_properly_formatted(value):
                            failed_fields.append(field_name)
                    elif expected_type == 'month_string':
                        if not self._is_month_properly_formatted(value):
                            failed_fields.append(field_name)
                    elif expected_type == 'employee_code':
                        if not self._is_employee_code_properly_formatted(value):
                            failed_fields.append(field_name)
                    elif expected_type == 'department_code':
                        if not self._is_department_code_properly_formatted(value):
                            failed_fields.append(field_name)
            
            if failed_fields:
                self.logger.warning(f"🔧 [格式化验证] 以下字段格式化不完整: {failed_fields}")
                return False
            
            return True
            
        except Exception as e:
            self.logger.error(f"🔧 [格式化验证] 验证异常: {e}")
            return False  # 验证异常时认为格式化不完整
    
    def _is_float_properly_formatted(self, value) -> bool:
        """检查浮点数是否正确格式化为两位小数或0.00"""
        try:
            if value is None or value == '':
                return True  # 空值允许

            value_str = str(value)

            # 🔧 [修复] 检查是否为标准的0.00格式（空值应该格式化为这个）
            if value_str == '0.00':
                return True

            # 🔧 [修复] 检查是否为两位小数格式
            if '.' in value_str:
                decimal_part = value_str.split('.')[-1]
                return len(decimal_part) == 2 and value_str.replace('.', '').replace('-', '').isdigit()
            else:
                # 🔧 [修复] 整数也可能是有效的格式化结果
                return value_str.replace('-', '').isdigit()

        except Exception:
            return False
    
    def _is_month_properly_formatted(self, value) -> bool:
        """检查月份是否正确格式化为两位字符串"""
        try:
            if value is None or value == '':
                return True  # 空值允许

            value_str = str(value)

            # 🔧 [修复] 如果包含小数点，说明格式化没有正确处理
            if '.' in value_str:
                # 类似"202501.0"的格式应该被认为是格式化不完整
                return False

            # 🔧 [修复] 检查是否为两位数字字符串（月份应该是01-12）
            if len(value_str) == 2 and value_str.isdigit():
                month_num = int(value_str)
                return 1 <= month_num <= 12

            # 🔧 [修复] 如果是更长的数字，可能需要提取后两位
            if len(value_str) > 2 and value_str.isdigit():
                # 这种情况说明格式化没有正确提取后两位
                return False

            return False

        except Exception:
            return False
    
    def _is_employee_code_properly_formatted(self, value) -> bool:
        """检查工号是否正确格式化（无小数点的整数字符串）"""
        try:
            if value is None or value == '':
                return True  # 空值允许

            value_str = str(value)

            # 🔧 [修复] 如果包含小数点，说明格式化没有正确处理
            if '.' in value_str:
                # 检查是否为类似"19990089.0"的格式，这种情况应该被认为是格式化不完整
                if value_str.endswith('.0'):
                    return False  # 应该去掉.0后缀
                else:
                    return False  # 其他包含小数点的情况也不符合要求

            # 检查是否为纯数字字符串
            return value_str.isdigit()

        except Exception:
            return False
    
    def _is_department_code_properly_formatted(self, value) -> bool:
        """检查部门代码是否正确格式化（两位补零的数字字符串）"""
        try:
            if value is None or value == '':
                return True  # 空值允许
            
            value_str = str(value)
            # 检查是否为两位数字字符串，且不包含小数点
            return len(value_str) == 2 and value_str.isdigit() and '.' not in value_str
            
        except Exception:
            return False


# 全局单例实例
_master_format_manager = None

def get_master_format_manager() -> MasterFormatManager:
    """获取全局主格式化管理器单例"""
    global _master_format_manager
    if _master_format_manager is None:
        _master_format_manager = MasterFormatManager()
    return _master_format_manager